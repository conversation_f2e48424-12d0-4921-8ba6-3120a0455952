"""
归属地查询微服务
提供独立的电话号码归属地查询服务，支持RESTful API接口
采用保守的微服务化策略，保持向后兼容性

设计原则：
1. 单一职责：专注于归属地查询功能
2. 无状态设计：每个请求独立处理
3. 向后兼容：保持与原有LocationManager的接口兼容
4. 容错设计：优雅处理各种异常情况
5. 性能优化：内置缓存和连接池
"""

import asyncio
import logging
import time
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from contextlib import asynccontextmanager

# 尝试导入FastAPI，如果不存在则提供降级方案
try:
    from fastapi import FastAPI, HTTPException, Query, Path
    from fastapi.responses import JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel, Field
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI不可用，将使用基础HTTP服务器")

# 导入现有的归属地管理器
try:
    from location_manager import LocationManager
    LOCATION_MANAGER_AVAILABLE = True
except ImportError:
    LOCATION_MANAGER_AVAILABLE = False
    logging.warning("LocationManager不可用，将使用模拟数据")


class LocationRequest(BaseModel):
    """归属地查询请求模型"""
    phone_number: str = Field(..., description="电话号码", example="13800138000")
    include_cache: bool = Field(True, description="是否使用缓存")
    
    
class LocationResponse(BaseModel):
    """归属地查询响应模型"""
    status: str = Field(..., description="响应状态", example="success")
    data: Optional[Dict[str, Any]] = Field(None, description="归属地数据")
    message: Optional[str] = Field(None, description="错误消息")
    processing_time_ms: float = Field(..., description="处理时间（毫秒）")
    cache_hit: bool = Field(False, description="是否命中缓存")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态", example="healthy")
    service: str = Field(..., description="服务名称", example="location")
    version: str = Field(..., description="服务版本", example="1.0.0")
    uptime_seconds: float = Field(..., description="运行时间（秒）")
    total_requests: int = Field(..., description="总请求数")
    cache_stats: Dict[str, Any] = Field(..., description="缓存统计")


class LocationMicroservice:
    """
    归属地查询微服务
    
    功能特性：
    - RESTful API接口
    - 异步处理支持
    - 内置缓存机制
    - 性能监控
    - 健康检查
    - 优雅关闭
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化归属地微服务
        
        Args:
            config: 服务配置字典
        """
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)
        
        # 服务状态
        self.start_time = time.time()
        self.total_requests = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 初始化归属地管理器
        self.location_manager = self._init_location_manager()
        
        # 初始化FastAPI应用（如果可用）
        if FASTAPI_AVAILABLE:
            self.app = self._create_fastapi_app()
        else:
            self.app = None
            self.logger.warning("FastAPI不可用，微服务功能受限")
        
        self.logger.info("归属地微服务初始化完成")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'host': '127.0.0.1',
            'port': 8001,
            'debug': False,
            'enable_cors': True,
            'cache_enabled': True,
            'cache_ttl': 3600,  # 缓存1小时
            'max_cache_size': 10000,
            'request_timeout': 30.0,
            'log_level': 'INFO'
        }
    
    def _init_location_manager(self) -> Optional[LocationManager]:
        """
        初始化归属地管理器
        
        Returns:
            LocationManager实例或None（如果不可用）
        """
        if LOCATION_MANAGER_AVAILABLE:
            try:
                manager = LocationManager()
                self.logger.info("LocationManager初始化成功")
                return manager
            except Exception as e:
                self.logger.error(f"LocationManager初始化失败: {e}")
                return None
        else:
            self.logger.warning("LocationManager不可用，将使用模拟数据")
            return None
    
    def _create_fastapi_app(self) -> FastAPI:
        """
        创建FastAPI应用
        
        Returns:
            配置好的FastAPI应用实例
        """
        # 创建应用实例
        app = FastAPI(
            title="归属地查询微服务",
            description="提供电话号码归属地查询的RESTful API服务",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 配置CORS
        if self.config.get('enable_cors', True):
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
        
        # 注册路由
        self._register_routes(app)
        
        return app
    
    def _register_routes(self, app: FastAPI):
        """
        注册API路由
        
        Args:
            app: FastAPI应用实例
        """
        
        @app.get("/health", response_model=HealthResponse)
        async def health_check():
            """健康检查接口"""
            uptime = time.time() - self.start_time
            
            cache_stats = {
                'total_requests': self.total_requests,
                'cache_hits': self.cache_hits,
                'cache_misses': self.cache_misses,
                'cache_hit_rate': (self.cache_hits / max(self.total_requests, 1)) * 100
            }
            
            return HealthResponse(
                status="healthy",
                service="location",
                version="1.0.0",
                uptime_seconds=uptime,
                total_requests=self.total_requests,
                cache_stats=cache_stats
            )
        
        @app.get("/api/v1/location/{phone_number}", response_model=LocationResponse)
        async def get_location_by_path(
            phone_number: str = Path(..., description="电话号码", example="13800138000"),
            include_cache: bool = Query(True, description="是否使用缓存")
        ):
            """
            根据电话号码查询归属地信息（路径参数）
            
            Args:
                phone_number: 电话号码
                include_cache: 是否使用缓存
                
            Returns:
                归属地查询结果
            """
            return await self._process_location_query(phone_number, include_cache)
        
        @app.post("/api/v1/location", response_model=LocationResponse)
        async def get_location_by_body(request: LocationRequest):
            """
            根据电话号码查询归属地信息（请求体）
            
            Args:
                request: 归属地查询请求
                
            Returns:
                归属地查询结果
            """
            return await self._process_location_query(
                request.phone_number, 
                request.include_cache
            )
        
        @app.get("/api/v1/batch-location")
        async def get_batch_locations(
            phone_numbers: str = Query(..., description="电话号码列表，逗号分隔", example="13800138000,13800138001"),
            include_cache: bool = Query(True, description="是否使用缓存")
        ):
            """
            批量查询归属地信息
            
            Args:
                phone_numbers: 电话号码列表（逗号分隔）
                include_cache: 是否使用缓存
                
            Returns:
                批量查询结果
            """
            phone_list = [phone.strip() for phone in phone_numbers.split(',') if phone.strip()]
            
            if not phone_list:
                raise HTTPException(status_code=400, detail="电话号码列表不能为空")
            
            if len(phone_list) > 100:  # 限制批量查询数量
                raise HTTPException(status_code=400, detail="批量查询数量不能超过100个")
            
            start_time = time.time()
            results = []
            
            for phone_number in phone_list:
                try:
                    result = await self._process_location_query(phone_number, include_cache)
                    results.append({
                        'phone_number': phone_number,
                        'location_data': result.data,
                        'status': result.status
                    })
                except Exception as e:
                    results.append({
                        'phone_number': phone_number,
                        'location_data': None,
                        'status': 'error',
                        'error': str(e)
                    })
            
            processing_time = (time.time() - start_time) * 1000
            
            return {
                'status': 'success',
                'data': {
                    'results': results,
                    'total_count': len(phone_list),
                    'success_count': sum(1 for r in results if r['status'] == 'success')
                },
                'processing_time_ms': processing_time
            }
        
        @app.get("/api/v1/stats")
        async def get_service_stats():
            """获取服务统计信息"""
            uptime = time.time() - self.start_time
            
            stats = {
                'service_info': {
                    'name': 'location-microservice',
                    'version': '1.0.0',
                    'uptime_seconds': uptime,
                    'uptime_formatted': self._format_uptime(uptime)
                },
                'request_stats': {
                    'total_requests': self.total_requests,
                    'requests_per_second': self.total_requests / max(uptime, 1)
                },
                'cache_stats': {
                    'cache_enabled': self.config.get('cache_enabled', True),
                    'cache_hits': self.cache_hits,
                    'cache_misses': self.cache_misses,
                    'cache_hit_rate': (self.cache_hits / max(self.total_requests, 1)) * 100
                },
                'location_manager_status': {
                    'available': self.location_manager is not None,
                    'type': 'LocationManager' if self.location_manager else 'Mock'
                }
            }
            
            return {'status': 'success', 'data': stats}
    
    async def _process_location_query(self, phone_number: str, include_cache: bool = True) -> LocationResponse:
        """
        处理归属地查询请求
        
        Args:
            phone_number: 电话号码
            include_cache: 是否使用缓存
            
        Returns:
            查询结果
        """
        start_time = time.time()
        self.total_requests += 1
        cache_hit = False
        
        try:
            # 验证电话号码格式
            if not self._validate_phone_number(phone_number):
                raise ValueError(f"无效的电话号码格式: {phone_number}")
            
            # 查询归属地信息
            if self.location_manager:
                # 使用真实的LocationManager
                location_data = self.location_manager.get_phone_location(phone_number)
                if location_data:
                    self.cache_hits += 1
                    cache_hit = True
                else:
                    self.cache_misses += 1
            else:
                # 使用模拟数据
                location_data = self._get_mock_location_data(phone_number)
                self.cache_misses += 1
            
            processing_time = (time.time() - start_time) * 1000
            
            if location_data:
                return LocationResponse(
                    status="success",
                    data=location_data,
                    processing_time_ms=processing_time,
                    cache_hit=cache_hit
                )
            else:
                return LocationResponse(
                    status="not_found",
                    message=f"未找到号码 {phone_number} 的归属地信息",
                    processing_time_ms=processing_time,
                    cache_hit=False
                )
                
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            self.logger.error(f"查询归属地失败: {e}")
            
            return LocationResponse(
                status="error",
                message=str(e),
                processing_time_ms=processing_time,
                cache_hit=False
            )
    
    def _validate_phone_number(self, phone_number: str) -> bool:
        """
        验证电话号码格式
        
        Args:
            phone_number: 电话号码
            
        Returns:
            是否为有效格式
        """
        if not phone_number:
            return False
        
        # 清理号码格式
        cleaned = phone_number.replace(' ', '').replace('-', '').replace('+86', '')
        
        # 检查长度和格式
        if len(cleaned) == 11 and cleaned.startswith(('13', '14', '15', '16', '17', '18', '19')):
            return True  # 手机号
        elif len(cleaned) >= 10 and cleaned.startswith(('0')):
            return True  # 固话
        
        return False
    
    def _get_mock_location_data(self, phone_number: str) -> Dict[str, Any]:
        """
        获取模拟归属地数据（当LocationManager不可用时使用）
        
        Args:
            phone_number: 电话号码
            
        Returns:
            模拟的归属地数据
        """
        # 简单的模拟逻辑
        if phone_number.startswith('138'):
            return {
                'province': '北京',
                'city': '北京',
                'isp': '中国移动',
                'phone_type': 'mobile',
                'area_code': '010',
                'source': 'mock'
            }
        elif phone_number.startswith('139'):
            return {
                'province': '广东',
                'city': '深圳',
                'isp': '中国移动',
                'phone_type': 'mobile',
                'area_code': '0755',
                'source': 'mock'
            }
        else:
            return {
                'province': '未知',
                'city': '未知',
                'isp': '未知',
                'phone_type': 'unknown',
                'area_code': '',
                'source': 'mock'
            }
    
    def _format_uptime(self, uptime_seconds: float) -> str:
        """
        格式化运行时间
        
        Args:
            uptime_seconds: 运行时间（秒）
            
        Returns:
            格式化的时间字符串
        """
        hours = int(uptime_seconds // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        seconds = int(uptime_seconds % 60)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def run(self, host: Optional[str] = None, port: Optional[int] = None):
        """
        运行微服务
        
        Args:
            host: 监听地址
            port: 监听端口
        """
        if not FASTAPI_AVAILABLE:
            self.logger.error("FastAPI不可用，无法启动微服务")
            return
        
        host = host or self.config.get('host', '127.0.0.1')
        port = port or self.config.get('port', 8001)
        
        self.logger.info(f"启动归属地微服务: http://{host}:{port}")
        self.logger.info(f"API文档: http://{host}:{port}/docs")
        
        try:
            uvicorn.run(
                self.app,
                host=host,
                port=port,
                log_level=self.config.get('log_level', 'info').lower()
            )
        except Exception as e:
            self.logger.error(f"微服务启动失败: {e}")
    
    # 向后兼容的同步接口
    def get_phone_location(self, phone_number: str) -> Dict[str, Any]:
        """
        同步获取电话号码归属地（向后兼容接口）
        
        Args:
            phone_number: 电话号码
            
        Returns:
            归属地信息字典
        """
        if self.location_manager:
            return self.location_manager.get_phone_location(phone_number)
        else:
            return self._get_mock_location_data(phone_number)


def create_location_microservice(config: Optional[Dict[str, Any]] = None) -> LocationMicroservice:
    """
    创建归属地微服务实例
    
    Args:
        config: 服务配置
        
    Returns:
        LocationMicroservice实例
    """
    return LocationMicroservice(config)


def main():
    """主函数：启动微服务"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建并启动微服务
    service = create_location_microservice()
    service.run()


if __name__ == '__main__':
    main()
