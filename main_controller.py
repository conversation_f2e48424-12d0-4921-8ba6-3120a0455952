"""
主控制程序
统一管理所有微服务和功能模块的主程序
提供完整的系统控制、配置管理、服务协调等功能

功能特性：
1. 统一的服务管理和控制
2. 配置文件管理
3. 服务健康监控
4. 自动故障恢复
5. 系统状态监控
6. 日志管理
7. 性能统计
"""

import os
import sys
import time
import json
import logging
import threading
import signal
import argparse
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import subprocess

# 导入所有微服务
try:
    from location_microservice import LocationMicroservice
    from nlp_microservice import NLPMicroservice
    from api_gateway import APIGateway
    from batch_processing_service import BatchProcessingService
    from web_admin_system import WebAdminSystem
    from service_monitor import create_service_monitor
    from enhanced_cache import create_enhanced_cache
    from distributed_tracing import create_tracer
    SERVICES_AVAILABLE = True
except ImportError as e:
    SERVICES_AVAILABLE = False
    print(f"警告: 部分服务不可用: {e}")


class ServiceStatus:
    """服务状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class MainController:
    """
    主控制程序
    
    功能特性：
    - 统一的服务生命周期管理
    - 配置文件管理
    - 服务健康监控
    - 自动故障恢复
    - 系统状态监控
    """
    
    def __init__(self, config_file: str = "system_config.json"):
        """
        初始化主控制程序
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.logger = self._setup_logging()
        
        # 服务实例
        self.services = {}
        self.service_status = {}
        self.service_threads = {}
        
        # 系统状态
        self.is_running = True
        self.start_time = time.time()
        
        # 监控和缓存
        self.system_monitor = None
        self.system_cache = None
        self.system_tracer = None
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("主控制程序初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "system": {
                "name": "电话号码标记识别系统",
                "version": "1.0.0",
                "debug": True,
                "log_level": "INFO",
                "log_dir": "./logs",
                "data_dir": "./data",
                "temp_dir": "./temp"
            },
            "services": {
                "location_service": {
                    "enabled": True,
                    "port": 8001,
                    "config": {
                        "cache_enabled": True,
                        "batch_size": 100
                    }
                },
                "nlp_service": {
                    "enabled": True,
                    "port": 8002,
                    "config": {
                        "cache_enabled": True,
                        "max_batch_size": 50
                    }
                },
                "api_gateway": {
                    "enabled": True,
                    "port": 8000,
                    "config": {
                        "rate_limit_requests_per_minute": 1000
                    }
                },
                "batch_processing": {
                    "enabled": True,
                    "port": 8003,
                    "config": {
                        "max_workers": 5,
                        "batch_size": 100
                    }
                },
                "web_admin": {
                    "enabled": True,
                    "port": 8080,
                    "config": {
                        "debug": True
                    }
                }
            },
            "database": {
                "host": "localhost",
                "user": "root",
                "password": "",
                "database": "phone_marking_system"
            },
            "monitoring": {
                "enabled": True,
                "interval": 30,
                "health_check_timeout": 10
            },
            "cache": {
                "enabled": True,
                "l1_max_size": 10000,
                "l1_ttl": 3600,
                "l2_enabled": False
            },
            "tracing": {
                "enabled": True,
                "sampling_rate": 1.0
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    self._merge_config(default_config, config)
                    return default_config
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
        
        # 保存默认配置
        self._save_config(default_config)
        return default_config
    
    def _merge_config(self, default: Dict[str, Any], custom: Dict[str, Any]):
        """合并配置"""
        for key, value in custom.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        log_dir = Path(self.config['system']['log_dir'])
        log_dir.mkdir(exist_ok=True)
        
        log_level = getattr(logging, self.config['system']['log_level'])
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / f"main_controller_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(log_level)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)
        
        # 配置根日志器
        logger = logging.getLogger('MainController')
        logger.setLevel(log_level)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"接收到信号 {signum}，正在关闭系统...")
        self.stop_all_services()
        sys.exit(0)
    
    def _init_system_components(self):
        """初始化系统组件"""
        if not SERVICES_AVAILABLE:
            self.logger.warning("服务模块不可用，部分功能受限")
            return
        
        try:
            # 初始化系统监控
            if self.config['monitoring']['enabled']:
                self.system_monitor = create_service_monitor("main-controller")
                self.logger.info("系统监控初始化完成")
            
            # 初始化系统缓存
            if self.config['cache']['enabled']:
                cache_config = {
                    'l1_cache': {
                        'max_size': self.config['cache']['l1_max_size'],
                        'default_ttl': self.config['cache']['l1_ttl']
                    },
                    'enable_l2_cache': self.config['cache']['l2_enabled']
                }
                self.system_cache = create_enhanced_cache(cache_config)
                self.logger.info("系统缓存初始化完成")
            
            # 初始化系统追踪
            if self.config['tracing']['enabled']:
                tracing_config = {
                    'sampling_rate': self.config['tracing']['sampling_rate']
                }
                self.system_tracer = create_tracer("main-controller", tracing_config)
                self.logger.info("系统追踪初始化完成")
            
        except Exception as e:
            self.logger.error(f"系统组件初始化失败: {e}")
    
    def start_service(self, service_name: str) -> bool:
        """启动指定服务"""
        if not SERVICES_AVAILABLE:
            self.logger.error("服务模块不可用")
            return False
        
        service_config = self.config['services'].get(service_name)
        if not service_config or not service_config.get('enabled', False):
            self.logger.warning(f"服务 {service_name} 未启用")
            return False
        
        if service_name in self.services:
            self.logger.warning(f"服务 {service_name} 已在运行")
            return True
        
        try:
            self.service_status[service_name] = ServiceStatus.STARTING
            self.logger.info(f"启动服务: {service_name}")
            
            # 根据服务名称创建相应的服务实例
            if service_name == "location_service":
                service = LocationMicroservice(service_config['config'])
            elif service_name == "nlp_service":
                service = NLPMicroservice(service_config['config'])
            elif service_name == "api_gateway":
                service = APIGateway(service_config['config'])
            elif service_name == "batch_processing":
                service = BatchProcessingService(service_config['config'])
            elif service_name == "web_admin":
                service = WebAdminSystem(service_config['config'])
            else:
                raise ValueError(f"未知的服务: {service_name}")
            
            # 启动服务
            if hasattr(service, 'run'):
                # 在单独的线程中运行服务
                def run_service():
                    try:
                        service.run(port=service_config['port'])
                    except Exception as e:
                        self.logger.error(f"服务 {service_name} 运行异常: {e}")
                        self.service_status[service_name] = ServiceStatus.ERROR
                
                thread = threading.Thread(target=run_service, daemon=True)
                thread.start()
                self.service_threads[service_name] = thread
            
            self.services[service_name] = service
            self.service_status[service_name] = ServiceStatus.RUNNING
            
            self.logger.info(f"服务 {service_name} 启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动服务 {service_name} 失败: {e}")
            self.service_status[service_name] = ServiceStatus.ERROR
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """停止指定服务"""
        if service_name not in self.services:
            self.logger.warning(f"服务 {service_name} 未在运行")
            return True
        
        try:
            self.service_status[service_name] = ServiceStatus.STOPPING
            self.logger.info(f"停止服务: {service_name}")
            
            service = self.services[service_name]
            
            # 停止服务
            if hasattr(service, 'stop'):
                service.stop()
            
            # 等待线程结束
            if service_name in self.service_threads:
                thread = self.service_threads[service_name]
                thread.join(timeout=10)
                del self.service_threads[service_name]
            
            del self.services[service_name]
            self.service_status[service_name] = ServiceStatus.STOPPED
            
            self.logger.info(f"服务 {service_name} 停止成功")
            return True
            
        except Exception as e:
            self.logger.error(f"停止服务 {service_name} 失败: {e}")
            self.service_status[service_name] = ServiceStatus.ERROR
            return False
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        self.logger.info("启动所有服务...")
        
        # 初始化系统组件
        self._init_system_components()
        
        # 按依赖顺序启动服务
        service_order = [
            "location_service",
            "nlp_service", 
            "batch_processing",
            "api_gateway",
            "web_admin"
        ]
        
        success_count = 0
        for service_name in service_order:
            if self.start_service(service_name):
                success_count += 1
                time.sleep(2)  # 服务间启动间隔
            else:
                self.logger.error(f"服务 {service_name} 启动失败，停止后续启动")
                break
        
        total_services = len([s for s in service_order if self.config['services'].get(s, {}).get('enabled', False)])
        
        self.logger.info(f"服务启动完成: {success_count}/{total_services}")
        
        if success_count == total_services:
            self.logger.info("🎉 所有服务启动成功！")
            self._print_service_info()
            return True
        else:
            self.logger.warning("⚠️ 部分服务启动失败")
            return False
    
    def stop_all_services(self) -> bool:
        """停止所有服务"""
        self.logger.info("停止所有服务...")
        
        # 按相反顺序停止服务
        service_names = list(self.services.keys())
        service_names.reverse()
        
        success_count = 0
        for service_name in service_names:
            if self.stop_service(service_name):
                success_count += 1
        
        # 停止系统组件
        if self.system_monitor:
            self.system_monitor.stop_monitoring()
        
        total_services = len(service_names)
        self.logger.info(f"服务停止完成: {success_count}/{total_services}")
        
        return success_count == total_services
    
    def _print_service_info(self):
        """打印服务信息"""
        print("\n" + "="*80)
        print("电话号码标记识别系统 - 服务状态")
        print("="*80)
        
        for service_name, status in self.service_status.items():
            service_config = self.config['services'].get(service_name, {})
            port = service_config.get('port', 'N/A')
            
            status_icon = {
                ServiceStatus.RUNNING: "🟢",
                ServiceStatus.STOPPED: "🔴", 
                ServiceStatus.ERROR: "🔴",
                ServiceStatus.STARTING: "🟡",
                ServiceStatus.STOPPING: "🟡"
            }.get(status, "⚪")
            
            print(f"{status_icon} {service_name.upper()}")
            print(f"   状态: {status}")
            print(f"   端口: {port}")
            if status == ServiceStatus.RUNNING:
                print(f"   访问地址: http://127.0.0.1:{port}")
            print()
        
        print("💡 使用 Ctrl+C 停止所有服务")
        print("📖 Web管理界面: http://127.0.0.1:8080")
        print("📖 API网关: http://127.0.0.1:8000")
        print("📖 API文档: http://127.0.0.1:8000/docs")
        print("="*80)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        uptime = time.time() - self.start_time
        
        return {
            'system_info': {
                'name': self.config['system']['name'],
                'version': self.config['system']['version'],
                'uptime_seconds': uptime,
                'uptime_formatted': self._format_uptime(uptime)
            },
            'services': {
                name: {
                    'status': status,
                    'port': self.config['services'].get(name, {}).get('port'),
                    'enabled': self.config['services'].get(name, {}).get('enabled', False)
                }
                for name, status in self.service_status.items()
            },
            'system_components': {
                'monitor_enabled': self.system_monitor is not None,
                'cache_enabled': self.system_cache is not None,
                'tracing_enabled': self.system_tracer is not None
            }
        }
    
    def _format_uptime(self, uptime_seconds: float) -> str:
        """格式化运行时间"""
        hours = int(uptime_seconds // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        seconds = int(uptime_seconds % 60)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def run_interactive_mode(self):
        """运行交互模式"""
        print("电话号码标记识别系统 - 主控制程序")
        print("输入 'help' 查看可用命令")
        
        while self.is_running:
            try:
                command = input("\n> ").strip().lower()
                
                if command == 'help':
                    self._print_help()
                elif command == 'start':
                    self.start_all_services()
                elif command == 'stop':
                    self.stop_all_services()
                elif command == 'status':
                    self._print_service_info()
                elif command == 'config':
                    print(json.dumps(self.config, indent=2, ensure_ascii=False))
                elif command in ['quit', 'exit']:
                    self.stop_all_services()
                    break
                elif command.startswith('start '):
                    service_name = command.split(' ', 1)[1]
                    self.start_service(service_name)
                elif command.startswith('stop '):
                    service_name = command.split(' ', 1)[1]
                    self.stop_service(service_name)
                else:
                    print("未知命令，输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                print("\n正在停止所有服务...")
                self.stop_all_services()
                break
            except EOFError:
                break
    
    def _print_help(self):
        """打印帮助信息"""
        print("""
可用命令:
  start                 - 启动所有服务
  stop                  - 停止所有服务
  start <service_name>  - 启动指定服务
  stop <service_name>   - 停止指定服务
  status                - 显示服务状态
  config                - 显示配置信息
  help                  - 显示此帮助信息
  quit/exit             - 退出程序

可用服务:
  location_service      - 归属地查询服务
  nlp_service          - NLP文本分析服务
  api_gateway          - API网关
  batch_processing     - 批量处理服务
  web_admin            - Web管理系统
        """)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='电话号码标记识别系统主控制程序')
    parser.add_argument('--config', '-c', default='system_config.json', help='配置文件路径')
    parser.add_argument('--mode', '-m', choices=['interactive', 'daemon'], default='interactive', help='运行模式')
    parser.add_argument('--action', '-a', choices=['start', 'stop', 'status'], help='执行动作')
    
    args = parser.parse_args()
    
    # 创建主控制器
    controller = MainController(args.config)
    
    try:
        if args.mode == 'daemon':
            # 守护进程模式
            if args.action == 'start':
                controller.start_all_services()
                # 保持运行
                while controller.is_running:
                    time.sleep(1)
            elif args.action == 'stop':
                controller.stop_all_services()
            elif args.action == 'status':
                status = controller.get_system_status()
                print(json.dumps(status, indent=2, ensure_ascii=False))
        else:
            # 交互模式
            if args.action:
                if args.action == 'start':
                    controller.start_all_services()
                elif args.action == 'stop':
                    controller.stop_all_services()
                elif args.action == 'status':
                    controller._print_service_info()
            
            controller.run_interactive_mode()
            
    except KeyboardInterrupt:
        print("\n正在停止系统...")
        controller.stop_all_services()
    except Exception as e:
        controller.logger.error(f"系统运行异常: {e}")
        controller.stop_all_services()


if __name__ == '__main__':
    main()
