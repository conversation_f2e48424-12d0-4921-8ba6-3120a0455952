"""
批量处理微服务
支持大规模号码数据的导入、识别和处理
采用保守的实施策略，支持断点续传和进度监控

功能特性：
1. 大规模数据导入（支持万级别号码）
2. 异步批量处理，支持进度监控
3. 断点续传和错误恢复
4. 多种文件格式支持
5. 性能优化和资源管理
"""

import asyncio
import logging
import time
import json
import os
import csv
import pandas as pd
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import queue
import uuid
from pathlib import Path

# 尝试导入FastAPI
try:
    from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
    from fastapi.responses import JSONResponse, FileResponse
    from pydantic import BaseModel, Field
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI不可用，批量处理功能受限")

# 导入现有的微服务
try:
    from location_microservice import LocationMicroservice
    from nlp_microservice import NLPMicroservice
    from advanced_sqlite_manager import AdvancedSQLiteManager, ShardConfig, ShardingStrategy
    from enhanced_cache import create_enhanced_cache
    from service_monitor import create_service_monitor
    from distributed_tracing import create_tracer
    SERVICES_AVAILABLE = True
except ImportError:
    SERVICES_AVAILABLE = False
    logging.warning("部分微服务不可用")


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class FileFormat(Enum):
    """支持的文件格式"""
    CSV = "csv"
    EXCEL = "excel"
    TXT = "txt"
    JSON = "json"


@dataclass
class BatchTask:
    """批量处理任务"""
    task_id: str
    user_id: str
    task_name: str
    file_path: str
    file_format: FileFormat
    total_count: int
    processed_count: int = 0
    success_count: int = 0
    failed_count: int = 0
    status: TaskStatus = TaskStatus.PENDING
    created_time: float = None
    started_time: Optional[float] = None
    completed_time: Optional[float] = None
    error_message: Optional[str] = None
    progress_percentage: float = 0.0
    estimated_remaining_time: Optional[float] = None

    def __post_init__(self):
        if self.created_time is None:
            self.created_time = time.time()

    def update_progress(self):
        """更新进度"""
        if self.total_count > 0:
            self.progress_percentage = (self.processed_count / self.total_count) * 100

        # 估算剩余时间
        if self.started_time and self.processed_count > 0:
            elapsed_time = time.time() - self.started_time
            avg_time_per_item = elapsed_time / self.processed_count
            remaining_items = self.total_count - self.processed_count
            self.estimated_remaining_time = remaining_items * avg_time_per_item


@dataclass
class ProcessingResult:
    """处理结果"""
    phone_number: str
    success: bool
    location_data: Optional[Dict[str, Any]] = None
    nlp_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    processing_time_ms: float = 0.0
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class BatchImportRequest(BaseModel):
    """批量导入请求"""
    task_name: str = Field(..., description="任务名称")
    user_id: str = Field(..., description="用户ID")
    file_format: str = Field(..., description="文件格式")
    enable_location: bool = Field(True, description="启用归属地识别")
    enable_nlp: bool = Field(True, description="启用NLP分析")
    batch_size: int = Field(100, description="批处理大小")
    max_concurrent: int = Field(10, description="最大并发数")


class TaskProgressResponse(BaseModel):
    """任务进度响应"""
    task_id: str
    status: str
    progress_percentage: float
    processed_count: int
    total_count: int
    success_count: int
    failed_count: int
    estimated_remaining_time: Optional[float]
    error_message: Optional[str]


class BatchProcessingService:
    """
    批量处理微服务

    功能特性：
    - 大规模数据导入和处理
    - 异步批量处理
    - 进度监控和断点续传
    - 多种文件格式支持
    - 性能优化和资源管理
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化批量处理服务

        Args:
            config: 服务配置
        """
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)

        # 任务管理
        self.tasks: Dict[str, BatchTask] = {}
        self.task_results: Dict[str, List[ProcessingResult]] = {}
        self.task_queue = queue.Queue()

        # 线程安全
        self._tasks_lock = threading.RLock()
        self._results_lock = threading.RLock()

        # 工作线程
        self.worker_threads = []
        self.is_running = True

        # 初始化微服务
        self._init_services()

        # 初始化数据库
        self._init_database()

        # 启动工作线程
        self._start_workers()

        # 创建FastAPI应用
        if FASTAPI_AVAILABLE:
            self.app = self._create_fastapi_app()
        else:
            self.app = None

        self.logger.info("批量处理微服务初始化完成")

    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'host': '127.0.0.1',
            'port': 8003,
            'max_workers': 5,
            'batch_size': 100,
            'max_concurrent': 10,
            'upload_dir': './uploads',
            'export_dir': './exports',
            'max_file_size': 100 * 1024 * 1024,  # 100MB
            'supported_formats': ['csv', 'excel', 'txt', 'json'],
            'database_config': {
                'host': 'localhost',
                'user': 'root',
                'password': '',
                'database': 'phone_marking_system'
            },
            'enable_cache': True,
            'enable_monitoring': True,
            'enable_tracing': True
        }

    def _init_services(self):
        """初始化微服务"""
        if not SERVICES_AVAILABLE:
            self.logger.warning("微服务不可用，部分功能受限")
            self.location_service = None
            self.nlp_service = None
            self.cache = None
            self.monitor = None
            self.tracer = None
            return

        try:
            # 初始化归属地服务
            self.location_service = LocationMicroservice({
                'cache_enabled': True,
                'batch_size': self.config.get('batch_size', 100)
            })

            # 初始化NLP服务
            self.nlp_service = NLPMicroservice({
                'cache_enabled': True,
                'max_batch_size': self.config.get('batch_size', 100)
            })

            # 初始化缓存
            if self.config.get('enable_cache', True):
                self.cache = create_enhanced_cache({
                    'l1_cache': {'max_size': 10000, 'default_ttl': 3600}
                })
            else:
                self.cache = None

            # 初始化监控
            if self.config.get('enable_monitoring', True):
                self.monitor = create_service_monitor("batch-processing-service")
                self._register_metrics()
            else:
                self.monitor = None

            # 初始化追踪
            if self.config.get('enable_tracing', True):
                self.tracer = create_tracer("batch-processing-service")
            else:
                self.tracer = None

            self.logger.info("微服务初始化完成")

        except Exception as e:
            self.logger.error(f"微服务初始化失败: {e}")
            self.location_service = None
            self.nlp_service = None
            self.cache = None
            self.monitor = None
            self.tracer = None

    def _register_metrics(self):
        """注册监控指标"""
        if not self.monitor:
            return

        self.task_counter = self.monitor.register_counter("tasks_total", "任务总数")
        self.processing_counter = self.monitor.register_counter("processing_total", "处理总数")
        self.success_counter = self.monitor.register_counter("success_total", "成功总数")
        self.failed_counter = self.monitor.register_counter("failed_total", "失败总数")
        self.processing_timer = self.monitor.register_timer("processing_duration", "处理耗时")
        self.active_tasks_gauge = self.monitor.register_gauge("active_tasks", "活跃任务数")

    def _init_database(self):
        """初始化数据库"""
        try:
            # 使用高级SQLite管理器
            db_config = ShardConfig(
                strategy=ShardingStrategy.FUNCTIONAL,
                auto_create_shards=True
            )
            self.db_manager = AdvancedSQLiteManager("batch_processing.db", db_config)

            # 创建任务表
            self._create_tables()

            self.logger.info("数据库初始化完成")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            self.db_manager = None

    def _create_tables(self):
        """创建数据库表"""
        if not self.db_manager:
            return

        # 创建任务表
        create_tasks_table = """
        CREATE TABLE IF NOT EXISTS batch_tasks (
            task_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            task_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_format TEXT NOT NULL,
            total_count INTEGER NOT NULL,
            processed_count INTEGER DEFAULT 0,
            success_count INTEGER DEFAULT 0,
            failed_count INTEGER DEFAULT 0,
            status TEXT DEFAULT 'pending',
            created_time REAL NOT NULL,
            started_time REAL,
            completed_time REAL,
            error_message TEXT,
            progress_percentage REAL DEFAULT 0.0,
            estimated_remaining_time REAL
        )
        """

        # 创建处理结果表
        create_results_table = """
        CREATE TABLE IF NOT EXISTS processing_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id TEXT NOT NULL,
            phone_number TEXT NOT NULL,
            success BOOLEAN NOT NULL,
            location_data TEXT,
            nlp_data TEXT,
            error_message TEXT,
            processing_time_ms REAL NOT NULL,
            timestamp REAL NOT NULL,
            FOREIGN KEY (task_id) REFERENCES batch_tasks (task_id)
        )
        """

        try:
            # 执行SQL
            conn = self.db_manager._get_connection("batch_tasks")
            conn.execute(create_tasks_table)
            conn.execute(create_results_table)
            conn.commit()

        except Exception as e:
            self.logger.error(f"创建数据库表失败: {e}")

    def _start_workers(self):
        """启动工作线程"""
        max_workers = self.config.get('max_workers', 5)

        for i in range(max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"BatchWorker-{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)

        self.logger.info(f"启动了 {max_workers} 个工作线程")

    def _worker_loop(self):
        """工作线程循环"""
        while self.is_running:
            try:
                # 从队列获取任务
                task_id = self.task_queue.get(timeout=1.0)

                if task_id:
                    self._process_task(task_id)
                    self.task_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"工作线程异常: {e}")
                continue

    def _process_task(self, task_id: str):
        """处理单个任务"""
        with self._tasks_lock:
            task = self.tasks.get(task_id)

        if not task:
            self.logger.error(f"任务不存在: {task_id}")
            return

        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_time = time.time()
            self._save_task_to_db(task)

            if self.monitor:
                self.active_tasks_gauge.increment()

            self.logger.info(f"开始处理任务: {task_id}")

            # 读取文件数据
            phone_numbers = self._read_file_data(task.file_path, task.file_format)

            if not phone_numbers:
                raise ValueError("文件中没有找到有效的电话号码")

            # 更新总数
            task.total_count = len(phone_numbers)

            # 批量处理
            batch_size = self.config.get('batch_size', 100)
            max_concurrent = self.config.get('max_concurrent', 10)

            for i in range(0, len(phone_numbers), batch_size):
                if task.status == TaskStatus.CANCELLED:
                    break

                batch = phone_numbers[i:i + batch_size]

                # 异步处理批次
                asyncio.run(self._process_batch(task, batch, max_concurrent))

                # 更新进度
                task.update_progress()
                self._save_task_to_db(task)

            # 任务完成
            if task.status != TaskStatus.CANCELLED:
                task.status = TaskStatus.COMPLETED
                task.completed_time = time.time()

            self._save_task_to_db(task)

            if self.monitor:
                self.active_tasks_gauge.decrement()

            self.logger.info(f"任务处理完成: {task_id}")

        except Exception as e:
            self.logger.error(f"任务处理失败: {task_id}, 错误: {e}")

            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_time = time.time()
            self._save_task_to_db(task)

            if self.monitor:
                self.active_tasks_gauge.decrement()
                self.failed_counter.increment()

    async def _process_batch(self, task: BatchTask, phone_numbers: List[str], max_concurrent: int):
        """异步处理批次"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_single_phone(phone_number: str) -> ProcessingResult:
            async with semaphore:
                return await self._process_single_phone(task.task_id, phone_number)

        # 并发处理
        tasks = [process_single_phone(phone) for phone in phone_numbers]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"处理异常: {result}")
                task.failed_count += 1
            elif isinstance(result, ProcessingResult):
                if result.success:
                    task.success_count += 1
                else:
                    task.failed_count += 1

                # 保存结果
                self._save_result_to_db(result)

                # 添加到内存结果
                with self._results_lock:
                    if task.task_id not in self.task_results:
                        self.task_results[task.task_id] = []
                    self.task_results[task.task_id].append(result)

            task.processed_count += 1

            if self.monitor:
                self.processing_counter.increment()

    async def _process_single_phone(self, task_id: str, phone_number: str) -> ProcessingResult:
        """处理单个电话号码"""
        start_time = time.time()

        try:
            if self.tracer:
                with self.tracer.trace("process_phone_number") as span:
                    if span:
                        span.add_tag("task_id", task_id)
                        span.add_tag("phone_number", phone_number)

                    return await self._do_process_phone(phone_number)
            else:
                return await self._do_process_phone(phone_number)

        except Exception as e:
            processing_time = (time.time() - start_time) * 1000

            return ProcessingResult(
                phone_number=phone_number,
                success=False,
                error_message=str(e),
                processing_time_ms=processing_time
            )

    async def _do_process_phone(self, phone_number: str) -> ProcessingResult:
        """执行电话号码处理"""
        start_time = time.time()

        # 检查缓存
        cache_key = f"phone_result_{phone_number}"
        if self.cache:
            cached_result = self.cache.get(cache_key)
            if cached_result:
                return cached_result

        location_data = None
        nlp_data = None

        try:
            # 归属地查询
            if self.location_service:
                location_result = await self.location_service._process_location_query(phone_number, True)
                if location_result.status == 'success':
                    location_data = location_result.data

            # NLP分析（如果有标记文本）
            if self.nlp_service and location_data:
                # 这里可以根据实际需求添加NLP分析逻辑
                pass

            processing_time = (time.time() - start_time) * 1000

            result = ProcessingResult(
                phone_number=phone_number,
                success=True,
                location_data=location_data,
                nlp_data=nlp_data,
                processing_time_ms=processing_time
            )

            # 缓存结果
            if self.cache:
                self.cache.set(cache_key, result, 3600)  # 缓存1小时

            if self.monitor:
                self.success_counter.increment()
                self.processing_timer.record(processing_time / 1000)

            return result

        except Exception as e:
            processing_time = (time.time() - start_time) * 1000

            if self.monitor:
                self.failed_counter.increment()

            return ProcessingResult(
                phone_number=phone_number,
                success=False,
                error_message=str(e),
                processing_time_ms=processing_time
            )

    def _read_file_data(self, file_path: str, file_format: FileFormat) -> List[str]:
        """读取文件数据"""
        phone_numbers = []

        try:
            if file_format == FileFormat.CSV:
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    for row in reader:
                        if row:  # 跳过空行
                            # 假设电话号码在第一列
                            phone = str(row[0]).strip()
                            if self._is_valid_phone(phone):
                                phone_numbers.append(phone)

            elif file_format == FileFormat.EXCEL:
                df = pd.read_excel(file_path)
                # 假设电话号码在第一列
                for phone in df.iloc[:, 0]:
                    phone = str(phone).strip()
                    if self._is_valid_phone(phone):
                        phone_numbers.append(phone)

            elif file_format == FileFormat.TXT:
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if self._is_valid_phone(phone):
                            phone_numbers.append(phone)

            elif file_format == FileFormat.JSON:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        for item in data:
                            if isinstance(item, str):
                                phone = item.strip()
                            elif isinstance(item, dict) and 'phone' in item:
                                phone = str(item['phone']).strip()
                            else:
                                continue

                            if self._is_valid_phone(phone):
                                phone_numbers.append(phone)

            self.logger.info(f"从文件读取到 {len(phone_numbers)} 个有效电话号码")
            return phone_numbers

        except Exception as e:
            self.logger.error(f"读取文件失败: {e}")
            raise

    def _is_valid_phone(self, phone: str) -> bool:
        """验证电话号码格式"""
        if not phone:
            return False

        # 移除常见的分隔符
        phone = phone.replace('-', '').replace(' ', '').replace('(', '').replace(')', '')

        # 基本长度检查
        if len(phone) < 7 or len(phone) > 15:
            return False

        # 检查是否全为数字
        return phone.isdigit()

    def _save_task_to_db(self, task: BatchTask):
        """保存任务到数据库"""
        if not self.db_manager:
            return

        try:
            conn = self.db_manager._get_connection("batch_tasks")

            # 使用REPLACE INTO实现插入或更新
            sql = """
            REPLACE INTO batch_tasks (
                task_id, user_id, task_name, file_path, file_format,
                total_count, processed_count, success_count, failed_count,
                status, created_time, started_time, completed_time,
                error_message, progress_percentage, estimated_remaining_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            conn.execute(sql, (
                task.task_id, task.user_id, task.task_name, task.file_path,
                task.file_format.value, task.total_count, task.processed_count,
                task.success_count, task.failed_count, task.status.value,
                task.created_time, task.started_time, task.completed_time,
                task.error_message, task.progress_percentage, task.estimated_remaining_time
            ))
            conn.commit()

        except Exception as e:
            self.logger.error(f"保存任务到数据库失败: {e}")

    def _save_result_to_db(self, result: ProcessingResult):
        """保存处理结果到数据库"""
        if not self.db_manager:
            return

        try:
            conn = self.db_manager._get_connection("processing_results")

            sql = """
            INSERT INTO processing_results (
                task_id, phone_number, success, location_data, nlp_data,
                error_message, processing_time_ms, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            # 将字典转换为JSON字符串
            location_json = json.dumps(result.location_data) if result.location_data else None
            nlp_json = json.dumps(result.nlp_data) if result.nlp_data else None

            conn.execute(sql, (
                result.phone_number,  # 这里需要task_id，但ProcessingResult中没有，需要修改
                result.phone_number,
                result.success,
                location_json,
                nlp_json,
                result.error_message,
                result.processing_time_ms,
                result.timestamp
            ))
            conn.commit()

        except Exception as e:
            self.logger.error(f"保存处理结果到数据库失败: {e}")

    def create_task(self, user_id: str, task_name: str, file_path: str,
                   file_format: str, **kwargs) -> str:
        """创建批量处理任务"""
        task_id = str(uuid.uuid4())

        try:
            format_enum = FileFormat(file_format.lower())
        except ValueError:
            raise ValueError(f"不支持的文件格式: {file_format}")

        # 验证文件存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 创建任务
        task = BatchTask(
            task_id=task_id,
            user_id=user_id,
            task_name=task_name,
            file_path=file_path,
            file_format=format_enum,
            total_count=0  # 将在处理时更新
        )

        with self._tasks_lock:
            self.tasks[task_id] = task

        # 保存到数据库
        self._save_task_to_db(task)

        # 添加到处理队列
        self.task_queue.put(task_id)

        if self.monitor:
            self.task_counter.increment()

        self.logger.info(f"创建批量处理任务: {task_id}")
        return task_id

    def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务进度"""
        with self._tasks_lock:
            task = self.tasks.get(task_id)

        if not task:
            return None

        return {
            'task_id': task.task_id,
            'status': task.status.value,
            'progress_percentage': task.progress_percentage,
            'processed_count': task.processed_count,
            'total_count': task.total_count,
            'success_count': task.success_count,
            'failed_count': task.failed_count,
            'estimated_remaining_time': task.estimated_remaining_time,
            'error_message': task.error_message,
            'created_time': task.created_time,
            'started_time': task.started_time,
            'completed_time': task.completed_time
        }

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._tasks_lock:
            task = self.tasks.get(task_id)

        if not task:
            return False

        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            return False

        task.status = TaskStatus.CANCELLED
        task.completed_time = time.time()
        self._save_task_to_db(task)

        self.logger.info(f"取消任务: {task_id}")
        return True

    def get_task_results(self, task_id: str, format: str = "json") -> Optional[str]:
        """获取任务结果"""
        with self._results_lock:
            results = self.task_results.get(task_id, [])

        if not results:
            return None

        if format.lower() == "json":
            return json.dumps([asdict(result) for result in results], indent=2, default=str)
        elif format.lower() == "csv":
            return self._export_results_to_csv(results)
        elif format.lower() == "excel":
            return self._export_results_to_excel(results, task_id)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

    def _export_results_to_csv(self, results: List[ProcessingResult]) -> str:
        """导出结果为CSV"""
        export_dir = Path(self.config.get('export_dir', './exports'))
        export_dir.mkdir(exist_ok=True)

        filename = f"results_{int(time.time())}.csv"
        filepath = export_dir / filename

        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入标题行
            writer.writerow([
                '电话号码', '处理状态', '省份', '城市', '运营商',
                '错误信息', '处理时间(ms)', '时间戳'
            ])

            # 写入数据行
            for result in results:
                location = result.location_data or {}
                writer.writerow([
                    result.phone_number,
                    '成功' if result.success else '失败',
                    location.get('province', ''),
                    location.get('city', ''),
                    location.get('isp', ''),
                    result.error_message or '',
                    result.processing_time_ms,
                    datetime.fromtimestamp(result.timestamp).strftime('%Y-%m-%d %H:%M:%S')
                ])

        return str(filepath)

    def _export_results_to_excel(self, results: List[ProcessingResult], task_id: str) -> str:
        """导出结果为Excel"""
        export_dir = Path(self.config.get('export_dir', './exports'))
        export_dir.mkdir(exist_ok=True)

        filename = f"results_{task_id}_{int(time.time())}.xlsx"
        filepath = export_dir / filename

        # 准备数据
        data = []
        for result in results:
            location = result.location_data or {}
            data.append({
                '电话号码': result.phone_number,
                '处理状态': '成功' if result.success else '失败',
                '省份': location.get('province', ''),
                '城市': location.get('city', ''),
                '运营商': location.get('isp', ''),
                '错误信息': result.error_message or '',
                '处理时间(ms)': result.processing_time_ms,
                '时间戳': datetime.fromtimestamp(result.timestamp).strftime('%Y-%m-%d %H:%M:%S')
            })

        # 创建DataFrame并导出
        df = pd.DataFrame(data)
        df.to_excel(filepath, index=False, engine='openpyxl')

        return str(filepath)

    def _create_fastapi_app(self) -> FastAPI:
        """创建FastAPI应用"""
        app = FastAPI(
            title="批量处理微服务",
            description="支持大规模号码数据的导入、识别和处理",
            version="1.0.0"
        )

        @app.post("/api/v1/upload")
        async def upload_file(
            background_tasks: BackgroundTasks,
            file: UploadFile = File(...),
            task_name: str = "批量处理任务",
            user_id: str = "default_user",
            file_format: str = "csv"
        ):
            """上传文件并创建批量处理任务"""
            try:
                # 验证文件大小
                max_size = self.config.get('max_file_size', 100 * 1024 * 1024)
                if file.size > max_size:
                    raise HTTPException(status_code=413, detail="文件过大")

                # 验证文件格式
                if file_format.lower() not in self.config.get('supported_formats', []):
                    raise HTTPException(status_code=400, detail="不支持的文件格式")

                # 保存上传的文件
                upload_dir = Path(self.config.get('upload_dir', './uploads'))
                upload_dir.mkdir(exist_ok=True)

                filename = f"{int(time.time())}_{file.filename}"
                filepath = upload_dir / filename

                with open(filepath, 'wb') as f:
                    content = await file.read()
                    f.write(content)

                # 创建批量处理任务
                task_id = self.create_task(
                    user_id=user_id,
                    task_name=task_name,
                    file_path=str(filepath),
                    file_format=file_format
                )

                return {
                    "status": "success",
                    "task_id": task_id,
                    "message": "文件上传成功，批量处理任务已创建"
                }

            except Exception as e:
                self.logger.error(f"文件上传失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @app.get("/api/v1/tasks/{task_id}/progress", response_model=TaskProgressResponse)
        async def get_task_progress(task_id: str):
            """获取任务进度"""
            progress = self.get_task_progress(task_id)
            if not progress:
                raise HTTPException(status_code=404, detail="任务不存在")

            return TaskProgressResponse(**progress)

        @app.post("/api/v1/tasks/{task_id}/cancel")
        async def cancel_task(task_id: str):
            """取消任务"""
            success = self.cancel_task(task_id)
            if not success:
                raise HTTPException(status_code=400, detail="无法取消任务")

            return {"status": "success", "message": "任务已取消"}

        @app.get("/api/v1/tasks/{task_id}/export")
        async def export_results(task_id: str, format: str = "csv"):
            """导出任务结果"""
            try:
                if format.lower() in ["csv", "excel"]:
                    filepath = self.get_task_results(task_id, format)
                    if not filepath:
                        raise HTTPException(status_code=404, detail="任务结果不存在")

                    return FileResponse(
                        filepath,
                        media_type='application/octet-stream',
                        filename=os.path.basename(filepath)
                    )
                else:
                    results_json = self.get_task_results(task_id, "json")
                    if not results_json:
                        raise HTTPException(status_code=404, detail="任务结果不存在")

                    return JSONResponse(content=json.loads(results_json))

            except Exception as e:
                self.logger.error(f"导出结果失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @app.get("/api/v1/tasks")
        async def list_tasks(user_id: str = None, status: str = None):
            """列出任务"""
            with self._tasks_lock:
                tasks = list(self.tasks.values())

            # 过滤条件
            if user_id:
                tasks = [t for t in tasks if t.user_id == user_id]
            if status:
                tasks = [t for t in tasks if t.status.value == status]

            return {
                "status": "success",
                "tasks": [asdict(task) for task in tasks]
            }

        @app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy",
                "service": "batch-processing",
                "active_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING]),
                "total_tasks": len(self.tasks)
            }

        return app

    def run(self, host: Optional[str] = None, port: Optional[int] = None):
        """运行微服务"""
        if not FASTAPI_AVAILABLE:
            self.logger.error("FastAPI不可用，无法启动微服务")
            return

        host = host or self.config.get('host', '127.0.0.1')
        port = port or self.config.get('port', 8003)

        self.logger.info(f"启动批量处理微服务: http://{host}:{port}")

        try:
            uvicorn.run(self.app, host=host, port=port)
        except Exception as e:
            self.logger.error(f"微服务启动失败: {e}")

    def stop(self):
        """停止服务"""
        self.is_running = False

        # 等待工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5)

        # 停止监控
        if self.monitor:
            self.monitor.stop_monitoring()


def create_batch_processing_service(config: Optional[Dict[str, Any]] = None) -> BatchProcessingService:
    """创建批量处理服务实例"""
    return BatchProcessingService(config)


def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    service = create_batch_processing_service()
    service.run()


if __name__ == '__main__':
    main()