"""
智能预处理选择器
基于历史数据和图像特征，智能选择最佳的预处理方法组合
采用保守的机器学习方法，确保系统稳定性
"""

import json
import os
import logging
import numpy as np
import cv2
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict
import pickle
from datetime import datetime, timedelta


class IntelligentPreprocessor:
    """智能预处理选择器"""
    
    def __init__(self, data_file: str = 'preprocessing_data.json'):
        self.logger = logging.getLogger(__name__)
        self.data_file = data_file
        self.model_file = 'preprocessing_model.pkl'
        
        # 预处理方法定义
        self.preprocessing_methods = {
            'grayscale': 'grayscale',
            'binary': 'binary',
            'adaptive_threshold': 'adaptive_threshold',
            'gaussian_blur': 'gaussian_blur',
            'denoise': 'denoise',
            'morphology': 'morphology',
            'median_blur': 'median_blur',
            'contrast_enhance': 'contrast_enhance'
        }
        
        # 历史数据存储
        self.historical_data = self._load_historical_data()
        
        # 简单的规则模型（保守方法）
        self.rule_model = self._init_rule_model()
        
        # 统计模型（可选，更高级的方法）
        self.statistical_model = None
        self._try_load_statistical_model()
        
        self.logger.info("智能预处理选择器初始化完成")
    
    def _load_historical_data(self) -> Dict[str, Any]:
        """加载历史数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.logger.info(f"加载历史数据成功，记录数: {len(data.get('records', []))}")
                return data
            except Exception as e:
                self.logger.warning(f"加载历史数据失败: {e}")
        
        # 返回默认结构
        return {
            'records': [],
            'method_stats': {},
            'image_feature_stats': {},
            'last_updated': datetime.now().isoformat()
        }
    
    def _save_historical_data(self):
        """保存历史数据"""
        try:
            self.historical_data['last_updated'] = datetime.now().isoformat()
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.historical_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存历史数据失败: {e}")
    
    def _init_rule_model(self) -> Dict[str, Any]:
        """初始化基于规则的模型（保守方法）"""
        return {
            # 基于图像特征的规则
            'brightness_rules': {
                'very_dark': ['contrast_enhance', 'adaptive_threshold'],
                'dark': ['adaptive_threshold', 'grayscale'],
                'normal': ['grayscale', 'adaptive_threshold'],
                'bright': ['grayscale', 'binary'],
                'very_bright': ['binary', 'morphology']
            },
            'contrast_rules': {
                'very_low': ['contrast_enhance', 'adaptive_threshold'],
                'low': ['adaptive_threshold', 'grayscale'],
                'normal': ['grayscale', 'adaptive_threshold'],
                'high': ['grayscale', 'binary'],
                'very_high': ['binary', 'morphology']
            },
            'noise_rules': {
                'very_high': ['denoise', 'gaussian_blur', 'adaptive_threshold'],
                'high': ['gaussian_blur', 'adaptive_threshold'],
                'normal': ['grayscale', 'adaptive_threshold'],
                'low': ['grayscale', 'binary'],
                'very_low': ['binary', 'morphology']
            },
            # 默认方法组合
            'default_methods': ['grayscale', 'adaptive_threshold', 'binary']
        }
    
    def _try_load_statistical_model(self):
        """尝试加载统计模型（可选的高级功能）"""
        if os.path.exists(self.model_file):
            try:
                with open(self.model_file, 'rb') as f:
                    self.statistical_model = pickle.load(f)
                self.logger.info("统计模型加载成功")
            except Exception as e:
                self.logger.warning(f"统计模型加载失败: {e}")
    
    def extract_image_features(self, image: np.ndarray) -> Dict[str, float]:
        """提取图像特征"""
        try:
            # 确保图像是灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 基础特征提取
            features = {}
            
            # 亮度特征
            features['brightness'] = float(np.mean(gray))
            features['brightness_std'] = float(np.std(gray))
            
            # 对比度特征
            features['contrast'] = float(np.std(gray))
            
            # 噪声估计（基于拉普拉斯算子）
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            features['noise_level'] = float(np.var(laplacian))
            
            # 边缘密度
            edges = cv2.Canny(gray, 50, 150)
            features['edge_density'] = float(np.sum(edges > 0) / edges.size)
            
            # 文本密度估计（基于连通组件）
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            num_labels, _ = cv2.connectedComponents(binary)
            features['text_density'] = float(num_labels / (gray.shape[0] * gray.shape[1]))
            
            # 图像尺寸特征
            features['width'] = float(gray.shape[1])
            features['height'] = float(gray.shape[0])
            features['aspect_ratio'] = features['width'] / features['height']
            
            return features
            
        except Exception as e:
            self.logger.error(f"图像特征提取失败: {e}")
            # 返回默认特征
            return {
                'brightness': 128.0,
                'brightness_std': 50.0,
                'contrast': 50.0,
                'noise_level': 100.0,
                'edge_density': 0.1,
                'text_density': 0.01,
                'width': 720.0,
                'height': 1280.0,
                'aspect_ratio': 0.56
            }
    
    def select_preprocessing_methods(self, image: np.ndarray, 
                                   device_info: Optional[Dict[str, Any]] = None,
                                   max_methods: int = 3) -> List[str]:
        """智能选择预处理方法"""
        try:
            # 提取图像特征
            features = self.extract_image_features(image)
            
            # 方法1：基于规则的选择（保守方法）
            rule_methods = self._select_by_rules(features)
            
            # 方法2：基于历史统计的选择（如果有足够数据）
            stat_methods = self._select_by_statistics(features)
            
            # 方法3：基于设备品牌的选择（如果有设备信息）
            brand_methods = self._select_by_brand(device_info) if device_info else []
            
            # 综合选择策略
            selected_methods = self._combine_selections(
                rule_methods, stat_methods, brand_methods, max_methods
            )
            
            self.logger.debug(f"选择的预处理方法: {selected_methods}")
            return selected_methods
            
        except Exception as e:
            self.logger.error(f"预处理方法选择失败: {e}")
            # 返回默认方法
            return ['grayscale', 'adaptive_threshold', 'binary']
    
    def _select_by_rules(self, features: Dict[str, float]) -> List[str]:
        """基于规则选择方法"""
        methods = []
        
        # 亮度规则
        brightness = features['brightness']
        if brightness < 50:
            methods.extend(self.rule_model['brightness_rules']['very_dark'])
        elif brightness < 100:
            methods.extend(self.rule_model['brightness_rules']['dark'])
        elif brightness < 180:
            methods.extend(self.rule_model['brightness_rules']['normal'])
        elif brightness < 220:
            methods.extend(self.rule_model['brightness_rules']['bright'])
        else:
            methods.extend(self.rule_model['brightness_rules']['very_bright'])
        
        # 对比度规则
        contrast = features['contrast']
        if contrast < 20:
            methods.extend(self.rule_model['contrast_rules']['very_low'])
        elif contrast < 40:
            methods.extend(self.rule_model['contrast_rules']['low'])
        elif contrast < 80:
            methods.extend(self.rule_model['contrast_rules']['normal'])
        elif contrast < 120:
            methods.extend(self.rule_model['contrast_rules']['high'])
        else:
            methods.extend(self.rule_model['contrast_rules']['very_high'])
        
        # 噪声规则
        noise = features['noise_level']
        if noise > 1000:
            methods.extend(self.rule_model['noise_rules']['very_high'])
        elif noise > 500:
            methods.extend(self.rule_model['noise_rules']['high'])
        elif noise > 200:
            methods.extend(self.rule_model['noise_rules']['normal'])
        elif noise > 50:
            methods.extend(self.rule_model['noise_rules']['low'])
        else:
            methods.extend(self.rule_model['noise_rules']['very_low'])
        
        # 去重并保持顺序
        unique_methods = []
        for method in methods:
            if method not in unique_methods:
                unique_methods.append(method)
        
        return unique_methods[:3]  # 最多返回3个方法
    
    def _select_by_statistics(self, features: Dict[str, float]) -> List[str]:
        """基于历史统计选择方法"""
        if not self.historical_data['records']:
            return []
        
        try:
            # 简单的统计方法：找到相似特征的历史记录
            similar_records = []
            
            for record in self.historical_data['records']:
                if 'features' not in record or 'success' not in record:
                    continue
                
                # 计算特征相似度
                similarity = self._calculate_feature_similarity(features, record['features'])
                if similarity > 0.7:  # 相似度阈值
                    similar_records.append((record, similarity))
            
            if not similar_records:
                return []
            
            # 按相似度排序
            similar_records.sort(key=lambda x: x[1], reverse=True)
            
            # 统计成功的方法
            method_success = defaultdict(list)
            for record, similarity in similar_records[:10]:  # 取前10个最相似的
                if record['success']:
                    for method in record.get('methods', []):
                        method_success[method].append(similarity)
            
            # 计算每个方法的加权成功率
            method_scores = {}
            for method, similarities in method_success.items():
                method_scores[method] = np.mean(similarities)
            
            # 按分数排序
            sorted_methods = sorted(method_scores.items(), key=lambda x: x[1], reverse=True)
            
            return [method for method, score in sorted_methods[:3]]
            
        except Exception as e:
            self.logger.warning(f"统计选择失败: {e}")
            return []
    
    def _select_by_brand(self, device_info: Dict[str, Any]) -> List[str]:
        """基于设备品牌选择方法"""
        try:
            # 这里可以集成品牌优化器的逻辑
            brand = device_info.get('brand', '').lower()
            
            brand_preferences = {
                'huawei': ['adaptive_threshold', 'morphology', 'contrast_enhance'],
                'xiaomi': ['gaussian_blur', 'adaptive_threshold', 'grayscale'],
                'samsung': ['contrast_enhance', 'adaptive_threshold', 'grayscale'],
                'oppo': ['adaptive_threshold', 'grayscale', 'binary'],
                'vivo': ['adaptive_threshold', 'grayscale', 'binary']
            }
            
            return brand_preferences.get(brand, [])
            
        except Exception as e:
            self.logger.warning(f"品牌选择失败: {e}")
            return []
    
    def _combine_selections(self, rule_methods: List[str], stat_methods: List[str], 
                          brand_methods: List[str], max_methods: int) -> List[str]:
        """综合多种选择策略"""
        # 权重分配
        weights = {
            'rule': 0.4,    # 规则方法权重
            'stat': 0.4,    # 统计方法权重
            'brand': 0.2    # 品牌方法权重
        }
        
        # 方法评分
        method_scores = defaultdict(float)
        
        # 规则方法评分
        for i, method in enumerate(rule_methods):
            method_scores[method] += weights['rule'] * (len(rule_methods) - i) / len(rule_methods)
        
        # 统计方法评分
        for i, method in enumerate(stat_methods):
            method_scores[method] += weights['stat'] * (len(stat_methods) - i) / len(stat_methods)
        
        # 品牌方法评分
        for i, method in enumerate(brand_methods):
            method_scores[method] += weights['brand'] * (len(brand_methods) - i) / len(brand_methods)
        
        # 按分数排序
        sorted_methods = sorted(method_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 确保包含基础方法
        final_methods = []
        essential_methods = ['grayscale', 'adaptive_threshold']
        
        for method in essential_methods:
            if method not in final_methods:
                final_methods.append(method)
        
        # 添加其他高分方法
        for method, score in sorted_methods:
            if method not in final_methods and len(final_methods) < max_methods:
                final_methods.append(method)
        
        return final_methods[:max_methods]
    
    def _calculate_feature_similarity(self, features1: Dict[str, float], 
                                    features2: Dict[str, float]) -> float:
        """计算特征相似度"""
        try:
            # 归一化特征
            normalized_features = ['brightness', 'contrast', 'noise_level', 'edge_density']
            
            similarities = []
            for feature in normalized_features:
                if feature in features1 and feature in features2:
                    # 使用相对差异计算相似度
                    val1, val2 = features1[feature], features2[feature]
                    max_val = max(val1, val2, 1.0)  # 避免除零
                    similarity = 1.0 - abs(val1 - val2) / max_val
                    similarities.append(max(0.0, similarity))
            
            return np.mean(similarities) if similarities else 0.0
            
        except Exception as e:
            self.logger.warning(f"相似度计算失败: {e}")
            return 0.0
    
    def record_result(self, image: np.ndarray, methods: List[str], 
                     success: bool, confidence: float = 0.0,
                     device_info: Optional[Dict[str, Any]] = None):
        """记录处理结果，用于改进选择算法"""
        try:
            features = self.extract_image_features(image)
            
            record = {
                'timestamp': datetime.now().isoformat(),
                'features': features,
                'methods': methods,
                'success': success,
                'confidence': confidence,
                'device_info': device_info
            }
            
            self.historical_data['records'].append(record)
            
            # 限制历史记录数量
            max_records = 1000
            if len(self.historical_data['records']) > max_records:
                self.historical_data['records'] = self.historical_data['records'][-max_records:]
            
            # 定期保存数据
            if len(self.historical_data['records']) % 10 == 0:
                self._save_historical_data()
            
        except Exception as e:
            self.logger.error(f"记录结果失败: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            records = self.historical_data['records']
            if not records:
                return {'message': '暂无历史数据'}
            
            # 统计各方法的成功率
            method_stats = defaultdict(lambda: {'total': 0, 'success': 0})
            
            for record in records:
                for method in record.get('methods', []):
                    method_stats[method]['total'] += 1
                    if record.get('success', False):
                        method_stats[method]['success'] += 1
            
            # 计算成功率
            method_success_rates = {}
            for method, stats in method_stats.items():
                if stats['total'] > 0:
                    method_success_rates[method] = stats['success'] / stats['total']
            
            return {
                'total_records': len(records),
                'method_success_rates': method_success_rates,
                'top_methods': sorted(method_success_rates.items(), 
                                    key=lambda x: x[1], reverse=True)[:5],
                'data_quality': 'good' if len(records) > 50 else 'limited'
            }
            
        except Exception as e:
            self.logger.error(f"生成性能报告失败: {e}")
            return {'error': str(e)}


def test_intelligent_preprocessor():
    """测试智能预处理选择器"""
    preprocessor = IntelligentPreprocessor()
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 测试特征提取
    features = preprocessor.extract_image_features(test_image)
    print("图像特征:", features)
    
    # 测试方法选择
    methods = preprocessor.select_preprocessing_methods(test_image)
    print("选择的方法:", methods)
    
    # 测试结果记录
    preprocessor.record_result(test_image, methods, True, 0.9)
    
    # 获取性能报告
    report = preprocessor.get_performance_report()
    print("性能报告:", report)


if __name__ == '__main__':
    test_intelligent_preprocessor()
