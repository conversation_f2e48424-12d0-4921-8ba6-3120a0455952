"""
并发处理管理器
采用保守的方式实现并发处理，确保系统稳定性
"""

import asyncio
import threading
import time
import logging
from typing import List, Dict, Any, Callable, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
import psutil
import os


class SafeConcurrentProcessor:
    """安全的并发处理器"""
    
    def __init__(self, max_workers: Optional[int] = None):
        self.logger = logging.getLogger(__name__)
        
        # 自动检测最佳并发数
        if max_workers is None:
            cpu_count = os.cpu_count() or 1
            # 保守策略：使用CPU核心数，最多不超过4
            self.max_workers = min(cpu_count, 4)
        else:
            self.max_workers = max_workers
        
        self.executor = None
        self.device_pool = Queue()
        self.processing_stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'avg_processing_time': 0.0,
            'concurrent_tasks': 0
        }
        
        # 资源监控
        self.resource_monitor = ResourceMonitor()
        
        # 初始化设备池
        self._init_device_pool()
        
        self.logger.info(f"并发处理器初始化完成，最大并发数: {self.max_workers}")
    
    def _init_device_pool(self):
        """初始化设备池"""
        try:
            # 这里可以扩展支持多设备
            # 目前先使用单设备模拟
            available_devices = self._get_available_devices()
            
            for device in available_devices:
                self.device_pool.put(device)
            
            self.logger.info(f"设备池初始化完成，可用设备数: {len(available_devices)}")
            
        except Exception as e:
            self.logger.error(f"设备池初始化失败: {e}")
            # 添加默认设备确保系统可以工作
            self.device_pool.put({'id': 'default', 'status': 'available'})
    
    def _get_available_devices(self) -> List[Dict[str, Any]]:
        """获取可用设备列表"""
        # 这里可以实现真实的设备检测逻辑
        # 目前返回模拟设备
        return [
            {'id': 'device_1', 'status': 'available', 'performance_score': 1.0}
        ]
    
    def process_batch_safe(self, items: List[Any], processor_func: Callable, 
                          timeout: float = 300.0) -> List[Dict[str, Any]]:
        """安全的批量处理"""
        if not items:
            return []
        
        # 检查系统资源
        if not self._check_system_resources():
            self.logger.warning("系统资源不足，降级为单线程处理")
            return self._process_sequential(items, processor_func)
        
        # 动态调整并发数
        actual_workers = min(self.max_workers, len(items))
        
        self.logger.info(f"开始批量处理 {len(items)} 个项目，并发数: {actual_workers}")
        
        try:
            with ThreadPoolExecutor(max_workers=actual_workers) as executor:
                self.executor = executor
                
                # 提交任务
                future_to_item = {}
                for item in items:
                    future = executor.submit(self._safe_process_single, item, processor_func)
                    future_to_item[future] = item
                
                # 收集结果
                results = []
                completed_count = 0
                
                for future in as_completed(future_to_item, timeout=timeout):
                    try:
                        result = future.result()
                        results.append(result)
                        completed_count += 1
                        
                        # 更新统计
                        if result.get('status') == 'success':
                            self.processing_stats['successful'] += 1
                        else:
                            self.processing_stats['failed'] += 1
                        
                        self.processing_stats['total_processed'] += 1
                        
                        # 进度报告
                        if completed_count % 10 == 0 or completed_count == len(items):
                            progress = (completed_count / len(items)) * 100
                            self.logger.info(f"处理进度: {completed_count}/{len(items)} ({progress:.1f}%)")
                        
                        # 检查资源使用情况
                        if completed_count % 5 == 0:
                            self._monitor_resources()
                        
                    except Exception as e:
                        item = future_to_item[future]
                        self.logger.error(f"处理项目失败: {item}, 错误: {e}")
                        results.append({
                            'item': item,
                            'status': 'failed',
                            'error': str(e)
                        })
                        self.processing_stats['failed'] += 1
                        self.processing_stats['total_processed'] += 1
                
                self.logger.info(f"批量处理完成，成功: {self.processing_stats['successful']}, "
                               f"失败: {self.processing_stats['failed']}")
                
                return results
                
        except Exception as e:
            self.logger.error(f"并发处理失败，降级为单线程: {e}")
            return self._process_sequential(items, processor_func)
        finally:
            self.executor = None
    
    def _safe_process_single(self, item: Any, processor_func: Callable) -> Dict[str, Any]:
        """安全的单项处理"""
        start_time = time.time()
        device = None
        
        try:
            # 获取设备
            device = self._get_device_safe()
            if not device:
                raise Exception("无法获取可用设备")
            
            # 更新并发任务计数
            self.processing_stats['concurrent_tasks'] += 1
            
            # 执行处理
            result = processor_func(item, device)
            
            # 确保返回标准格式
            if not isinstance(result, dict):
                result = {'item': item, 'result': result, 'status': 'success'}
            elif 'status' not in result:
                result['status'] = 'success'
            
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"单项处理失败: {item}, 错误: {e}")
            return {
                'item': item,
                'status': 'failed',
                'error': str(e),
                'processing_time': processing_time
            }
        finally:
            # 归还设备
            if device:
                self._return_device_safe(device)
            
            # 更新并发任务计数
            self.processing_stats['concurrent_tasks'] -= 1
    
    def _get_device_safe(self, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """安全获取设备"""
        try:
            return self.device_pool.get(timeout=timeout)
        except Empty:
            self.logger.warning("获取设备超时")
            return None
    
    def _return_device_safe(self, device: Dict[str, Any]):
        """安全归还设备"""
        try:
            self.device_pool.put(device)
        except Exception as e:
            self.logger.error(f"归还设备失败: {e}")
    
    def _process_sequential(self, items: List[Any], processor_func: Callable) -> List[Dict[str, Any]]:
        """单线程顺序处理（降级方案）"""
        self.logger.info(f"使用单线程处理 {len(items)} 个项目")
        
        results = []
        for i, item in enumerate(items):
            try:
                result = self._safe_process_single(item, processor_func)
                results.append(result)
                
                # 进度报告
                if (i + 1) % 10 == 0 or (i + 1) == len(items):
                    progress = ((i + 1) / len(items)) * 100
                    self.logger.info(f"处理进度: {i + 1}/{len(items)} ({progress:.1f}%)")
                
            except Exception as e:
                self.logger.error(f"顺序处理失败: {item}, 错误: {e}")
                results.append({
                    'item': item,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return results
    
    def _check_system_resources(self) -> bool:
        """检查系统资源是否足够支持并发处理"""
        try:
            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 80:
                self.logger.warning(f"CPU使用率过高: {cpu_percent}%")
                return False
            
            # 检查内存使用率
            memory = psutil.virtual_memory()
            if memory.percent > 85:
                self.logger.warning(f"内存使用率过高: {memory.percent}%")
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"资源检查失败: {e}")
            return True  # 检查失败时允许并发，保持功能可用
    
    def _monitor_resources(self):
        """监控资源使用情况"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            if cpu_percent > 90 or memory.percent > 90:
                self.logger.warning(f"资源使用率告警 - CPU: {cpu_percent}%, 内存: {memory.percent}%")
                
        except Exception as e:
            self.logger.debug(f"资源监控失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        total = self.processing_stats['total_processed']
        success_rate = (self.processing_stats['successful'] / max(total, 1)) * 100
        
        return {
            'total_processed': total,
            'successful': self.processing_stats['successful'],
            'failed': self.processing_stats['failed'],
            'success_rate': round(success_rate, 1),
            'concurrent_tasks': self.processing_stats['concurrent_tasks'],
            'max_workers': self.max_workers
        }
    
    def shutdown(self):
        """关闭并发处理器"""
        if self.executor:
            self.executor.shutdown(wait=True)
        self.logger.info("并发处理器已关闭")


class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                'cpu_count': os.cpu_count(),
                'cpu_percent': psutil.cpu_percent(),
                'memory_total': psutil.virtual_memory().total,
                'memory_available': psutil.virtual_memory().available,
                'memory_percent': psutil.virtual_memory().percent
            }
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {}


def test_concurrent_processor():
    """测试并发处理器"""
    def mock_processor(item, device):
        """模拟处理函数"""
        time.sleep(0.1)  # 模拟处理时间
        return {
            'item': item,
            'device': device['id'],
            'result': f"processed_{item}",
            'status': 'success'
        }
    
    processor = SafeConcurrentProcessor(max_workers=2)
    
    # 测试批量处理
    test_items = [f"item_{i}" for i in range(10)]
    
    print("开始并发处理测试...")
    start_time = time.time()
    
    results = processor.process_batch_safe(test_items, mock_processor)
    
    end_time = time.time()
    
    print(f"处理完成，耗时: {end_time - start_time:.2f}秒")
    print(f"结果数量: {len(results)}")
    print("统计信息:", processor.get_stats())
    
    processor.shutdown()


if __name__ == '__main__':
    test_concurrent_processor()
