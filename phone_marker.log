2025-06-30 16:50:23,349 - INFO - 检测到手机: {'brand': 'honor', 'model': 'LGE-AN00', 'resolution': [1224, 2664], 'android_version': '15'}
2025-06-30 16:50:40,230 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 16:50:40,231 - INFO - 
进度: 1/1
2025-06-30 16:50:40,231 - INFO - 正在使用ML Kit分析号码: 05925207403 (尝试 1/4)
2025-06-30 16:50:40,568 - INFO - 等待界面加载 2.0 秒...
2025-06-30 16:50:43,680 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 16:50:43,681 - INFO - 开始自动挂断...
2025-06-30 16:50:45,853 - INFO - 自动挂断完成
2025-06-30 16:50:45,854 - INFO - 使用ML Kit分析图像...
2025-06-30 16:50:59,472 - INFO - ML Kit检测到标记信息: 广告推销 (置信度: 1.05)
2025-06-30 16:50:59,474 - INFO - 还检测到其他信息: ['05925207403 福建厦门 | 14 人标记过', '视频通话              静音               联系人']
2025-06-30 16:50:59,476 - INFO - 处理完成: 05925207403 - 广告推销 (耗时: 19.24秒)
2025-06-30 16:50:59,476 - INFO - 
批量处理完成！
2025-06-30 16:50:59,476 - INFO - 总耗时: 19.25秒
2025-06-30 16:50:59,476 - INFO - 成功: 1个
2025-06-30 16:50:59,476 - INFO - 失败: 0个
2025-06-30 16:50:59,476 - INFO - 成功率: 100.0%
2025-06-30 16:50:59,476 - INFO - 正在自动导出结果...
2025-06-30 16:50:59,601 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_165059.xlsx
2025-06-30 16:55:22,808 - INFO - 检测到手机: {'brand': 'honor', 'model': 'LGE-AN00', 'resolution': [1224, 2664], 'android_version': '15'}
2025-06-30 16:55:35,792 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 16:55:35,793 - INFO - 
进度: 1/1
2025-06-30 16:55:35,793 - INFO - 正在使用ML Kit分析号码: 05925207403 (尝试 1/4)
2025-06-30 16:55:35,996 - INFO - 等待界面加载 2.0 秒...
2025-06-30 16:55:39,099 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 16:55:39,099 - INFO - 开始自动挂断...
2025-06-30 16:55:40,969 - INFO - 自动挂断完成
2025-06-30 16:55:40,969 - INFO - 使用ML Kit分析图像...
2025-06-30 16:55:54,632 - INFO - 检测到归属地: 厦门
2025-06-30 16:55:54,634 - INFO - ML Kit检测到标记信息: 广告推销 (置信度: 1.05)
2025-06-30 16:55:54,634 - INFO - 还检测到其他信息: ['05925207403 福建厦门114 人标记过', '视频通话              静音               联系人']
2025-06-30 16:55:54,636 - INFO - 处理完成: 05925207403 - 广告推销 (耗时: 18.84秒)
2025-06-30 16:55:54,636 - INFO - 
批量处理完成！
2025-06-30 16:55:54,636 - INFO - 总耗时: 18.84秒
2025-06-30 16:55:54,636 - INFO - 成功: 1个
2025-06-30 16:55:54,636 - INFO - 失败: 0个
2025-06-30 16:55:54,636 - INFO - 成功率: 100.0%
2025-06-30 16:55:54,636 - INFO - 正在自动导出结果...
2025-06-30 16:55:54,744 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_165554.xlsx
2025-06-30 17:14:24,265 - INFO - 检测到手机: {'brand': '', 'model': '', 'resolution': [1080, 2400], 'android_version': ''}
2025-06-30 17:14:31,745 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 17:14:31,746 - INFO - 
进度: 1/1
2025-06-30 17:14:31,746 - INFO - 正在使用ML Kit分析号码: 18638619770 (尝试 1/4)
2025-06-30 17:14:31,772 - INFO - 等待界面加载 2.0 秒...
2025-06-30 17:14:33,830 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 17:14:33,830 - INFO - 开始自动挂断...
2025-06-30 17:14:35,351 - INFO - 自动挂断完成
2025-06-30 17:14:35,353 - INFO - 使用ML Kit分析图像...
2025-06-30 17:14:35,372 - ERROR - 无法读取图像: screenshots/screenshot_20250630_171433.png
2025-06-30 17:14:35,374 - INFO - 处理完成: 18638619770 - 未检测到标记信息 (耗时: 3.63秒)
2025-06-30 17:14:35,374 - INFO - 
批量处理完成！
2025-06-30 17:14:35,374 - INFO - 总耗时: 3.63秒
2025-06-30 17:14:35,374 - INFO - 成功: 1个
2025-06-30 17:14:35,374 - INFO - 失败: 0个
2025-06-30 17:14:35,374 - INFO - 成功率: 100.0%
2025-06-30 17:14:35,374 - INFO - 正在自动导出结果...
2025-06-30 17:14:35,495 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_171435.xlsx
2025-06-30 17:15:07,283 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 17:15:07,285 - INFO - 
进度: 1/1
2025-06-30 17:15:07,285 - INFO - 正在使用ML Kit分析号码: 18638619770 (尝试 1/4)
2025-06-30 17:15:07,316 - INFO - 等待界面加载 2.0 秒...
2025-06-30 17:15:09,377 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 17:15:09,377 - INFO - 开始自动挂断...
2025-06-30 17:15:10,905 - INFO - 自动挂断完成
2025-06-30 17:15:10,906 - INFO - 使用ML Kit分析图像...
2025-06-30 17:15:10,906 - ERROR - 无法读取图像: screenshots/screenshot_20250630_171509.png
2025-06-30 17:15:10,911 - INFO - 处理完成: 18638619770 - 未检测到标记信息 (耗时: 3.62秒)
2025-06-30 17:15:10,911 - INFO - 
批量处理完成！
2025-06-30 17:15:10,911 - INFO - 总耗时: 3.63秒
2025-06-30 17:15:10,911 - INFO - 成功: 1个
2025-06-30 17:15:10,911 - INFO - 失败: 0个
2025-06-30 17:15:10,911 - INFO - 成功率: 100.0%
2025-06-30 17:15:10,911 - INFO - 正在自动导出结果...
2025-06-30 17:15:10,934 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_171510.xlsx
2025-06-30 17:16:15,166 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 17:16:15,167 - INFO - 
进度: 1/1
2025-06-30 17:16:15,167 - INFO - 正在使用ML Kit分析号码: 18638619770 (尝试 1/4)
2025-06-30 17:16:15,196 - INFO - 等待界面加载 2.0 秒...
2025-06-30 17:16:17,238 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 17:16:17,239 - INFO - 开始自动挂断...
2025-06-30 17:16:18,773 - INFO - 自动挂断完成
2025-06-30 17:16:18,774 - INFO - 使用ML Kit分析图像...
2025-06-30 17:16:18,775 - ERROR - 无法读取图像: screenshots/screenshot_20250630_171617.png
2025-06-30 17:16:18,778 - INFO - 处理完成: 18638619770 - 未检测到标记信息 (耗时: 3.61秒)
2025-06-30 17:16:18,779 - INFO - 
批量处理完成！
2025-06-30 17:16:18,779 - INFO - 总耗时: 3.61秒
2025-06-30 17:16:18,779 - INFO - 成功: 1个
2025-06-30 17:16:18,779 - INFO - 失败: 0个
2025-06-30 17:16:18,779 - INFO - 成功率: 100.0%
2025-06-30 17:16:18,779 - INFO - 正在自动导出结果...
2025-06-30 17:16:18,797 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_171618.xlsx
2025-06-30 17:16:33,000 - INFO - 检测到手机: {'brand': '', 'model': '', 'resolution': [1080, 2400], 'android_version': ''}
2025-06-30 17:30:58,547 - INFO - 检测到手机: {'brand': 'vivo', 'model': 'V2366GA', 'resolution': [1080, 2400], 'android_version': '15'}
2025-06-30 17:32:17,580 - INFO - 检测到手机: {'brand': 'vivo', 'model': 'V2366GA', 'resolution': [1080, 2400], 'android_version': '15'}
2025-06-30 17:32:26,096 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 17:32:26,096 - INFO - 
进度: 1/1
2025-06-30 17:32:26,096 - INFO - 正在使用ML Kit分析号码: 18638619770 (尝试 1/4)
2025-06-30 17:32:26,263 - INFO - 等待界面加载 2.0 秒...
2025-06-30 17:32:28,710 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 17:32:28,710 - INFO - 开始自动挂断...
2025-06-30 17:32:30,416 - INFO - 自动挂断完成
2025-06-30 17:32:30,417 - INFO - 使用ML Kit分析图像...
2025-06-30 17:32:38,468 - INFO - ML Kit检测到标记信息: iY vivo账号、云服务、查找设备等 (置信度: 0.80)
2025-06-30 17:32:38,470 - INFO - 处理完成: 18638619770 - iY vivo账号、云服务、查找设备等 (耗时: 12.37秒)
2025-06-30 17:32:38,470 - INFO - 
批量处理完成！
2025-06-30 17:32:38,470 - INFO - 总耗时: 12.37秒
2025-06-30 17:32:38,470 - INFO - 成功: 1个
2025-06-30 17:32:38,470 - INFO - 失败: 0个
2025-06-30 17:32:38,470 - INFO - 成功率: 100.0%
2025-06-30 17:32:38,470 - INFO - 正在自动导出结果...
2025-06-30 17:32:38,585 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_173238.xlsx
2025-06-30 17:32:57,484 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 17:32:57,484 - INFO - 
进度: 1/1
2025-06-30 17:32:57,485 - INFO - 正在使用ML Kit分析号码: 18638619770 (尝试 1/4)
2025-06-30 17:32:57,644 - INFO - 等待界面加载 2.0 秒...
2025-06-30 17:33:00,449 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 17:33:00,449 - INFO - 开始自动挂断...
2025-06-30 17:33:02,249 - INFO - 自动挂断完成
2025-06-30 17:33:02,250 - INFO - 使用ML Kit分析图像...
2025-06-30 17:33:15,846 - INFO - 检测到归属地: 郑州
2025-06-30 17:33:15,847 - INFO - ML Kit检测到标记信息: (全          人SN电话助手 (置信度: 0.80)
2025-06-30 17:33:15,848 - INFO - 还检测到其他信息: ['归属地: 郑州']
2025-06-30 17:33:15,849 - INFO - 处理完成: 18638619770 - (全          人SN电话助手 (耗时: 18.36秒)
2025-06-30 17:33:15,849 - INFO - 
批量处理完成！
2025-06-30 17:33:15,849 - INFO - 总耗时: 18.37秒
2025-06-30 17:33:15,850 - INFO - 成功: 1个
2025-06-30 17:33:15,850 - INFO - 失败: 0个
2025-06-30 17:33:15,850 - INFO - 成功率: 100.0%
2025-06-30 17:33:15,850 - INFO - 正在自动导出结果...
2025-06-30 17:33:15,859 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_173315.xlsx
2025-06-30 17:36:57,486 - ERROR - 设备未连接
2025-06-30 17:42:20,511 - ERROR - 设备未连接
2025-06-30 17:53:24,116 - INFO - 检测到手机: {'brand': 'huawei', 'model': 'MED-AL00', 'resolution': [720, 1600], 'android_version': '10'}
2025-06-30 17:53:32,457 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 17:53:32,458 - INFO - 
进度: 1/1
2025-06-30 17:53:32,459 - INFO - 正在使用ML Kit分析号码: 18638619770 (尝试 1/4)
2025-06-30 17:53:32,603 - INFO - 等待界面加载 2.0 秒...
2025-06-30 17:53:36,547 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 17:53:36,548 - INFO - 开始自动挂断...
2025-06-30 17:53:41,593 - INFO - 自动挂断完成
2025-06-30 17:53:41,595 - INFO - 使用ML Kit分析图像...
2025-06-30 17:53:50,867 - INFO - 检测到归属地: 郑州
2025-06-30 17:53:50,872 - INFO - ML Kit检测到标记信息: 视频通话         静音          联系人 (置信度: 1.00)
2025-06-30 17:53:50,873 - INFO - 还检测到其他信息: ['a=]      联系人', '静音      联系人']
2025-06-30 17:53:50,877 - INFO - 处理完成: 18638619770 - 视频通话         静音          联系人 (耗时: 18.41秒)
2025-06-30 17:53:50,877 - INFO - 
批量处理完成！
2025-06-30 17:53:50,877 - INFO - 总耗时: 18.42秒
2025-06-30 17:53:50,877 - INFO - 成功: 1个
2025-06-30 17:53:50,877 - INFO - 失败: 0个
2025-06-30 17:53:50,877 - INFO - 成功率: 100.0%
2025-06-30 17:53:50,877 - INFO - 正在自动导出结果...
2025-06-30 17:53:50,996 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_175350.xlsx
2025-06-30 18:15:54,437 - INFO - 归属地数据库初始化完成
2025-06-30 18:15:54,437 - INFO - 归属地查询功能已启用
2025-06-30 18:15:54,687 - INFO - 检测到手机: {'brand': 'huawei', 'model': 'MED-AL00', 'resolution': [720, 1600], 'android_version': '10'}
2025-06-30 18:16:15,482 - INFO - 开始批量ML Kit检测，共 1 个号码
2025-06-30 18:16:15,482 - INFO - 
进度: 1/1
2025-06-30 18:16:15,482 - INFO - 正在使用ML Kit分析号码: 18638619770 (尝试 1/4)
2025-06-30 18:16:15,621 - INFO - 等待界面加载 2.0 秒...
2025-06-30 18:16:19,508 - INFO - 截图完成，立即挂断，避免打扰对方...
2025-06-30 18:16:19,508 - INFO - 开始自动挂断...
2025-06-30 18:16:24,038 - INFO - 自动挂断完成
2025-06-30 18:16:24,039 - INFO - 使用ML Kit分析图像...
2025-06-30 18:16:35,228 - INFO - ML Kit检测到标记信息: 上次使用此卡联系 (置信度: 1.05)
2025-06-30 18:16:35,229 - INFO - 还检测到其他信息: ['乙己年人六月初六', '、 上次使用此卡联系']
2025-06-30 18:16:35,232 - INFO - 数据库查询归属地: 河南 郑州 中国联通
2025-06-30 18:16:35,233 - INFO - 处理完成: 18638619770 - 上次使用此卡联系 (耗时: 19.75秒)
2025-06-30 18:16:35,233 - INFO - 
批量处理完成！
2025-06-30 18:16:35,233 - INFO - 总耗时: 19.75秒
2025-06-30 18:16:35,233 - INFO - 成功: 1个
2025-06-30 18:16:35,233 - INFO - 失败: 0个
2025-06-30 18:16:35,233 - INFO - 成功率: 100.0%
2025-06-30 18:16:35,233 - INFO - 正在自动导出结果...
2025-06-30 18:16:35,354 - INFO - ✅ 结果已自动导出到: phone_mark_results_20250630_181635.xlsx
