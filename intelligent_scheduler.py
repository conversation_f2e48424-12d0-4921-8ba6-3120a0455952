"""
智能调度优化器
基于设备性能和历史数据，智能调度任务和优化重试策略
采用保守的方法，确保系统稳定性
"""

import json
import os
import time
import logging
import random
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading


class IntelligentScheduler:
    """智能调度管理器"""
    
    def __init__(self, config_file: str = 'scheduler_config.json'):
        self.logger = logging.getLogger(__name__)
        self.config_file = config_file
        self.stats_file = 'scheduler_stats.json'
        
        # 加载配置
        self.config = self._load_config()
        
        # 设备性能统计
        self.device_stats = self._load_device_stats()
        
        # 重试策略
        self.retry_strategies = {
            'exponential_backoff': [1, 2, 4, 8, 16],
            'linear_backoff': [1, 2, 3, 4, 5],
            'fibonacci_backoff': [1, 1, 2, 3, 5, 8],
            'fixed_interval': [2, 2, 2, 2, 2],
            'adaptive': []  # 动态计算
        }
        
        # 任务队列和优先级
        self.task_queue = deque()
        self.priority_weights = {
            'high': 3.0,
            'normal': 1.0,
            'low': 0.5
        }
        
        # 性能监控
        self.performance_history = deque(maxlen=100)
        self._lock = threading.Lock()
        
        self.logger.info("智能调度器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载调度配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info("调度配置加载成功")
                return config
            except Exception as e:
                self.logger.warning(f"加载调度配置失败: {e}")
        
        # 默认配置
        default_config = {
            'max_retry_attempts': 3,
            'default_timeout': 30.0,
            'device_selection_strategy': 'performance_based',
            'retry_strategy': 'adaptive',
            'performance_weight': 0.6,
            'availability_weight': 0.4,
            'failure_penalty': 0.2,
            'success_bonus': 0.1,
            'min_device_score': 0.3,
            'adaptive_retry_base': 1.5,
            'adaptive_retry_max': 30.0
        }
        
        self._save_config(default_config)
        return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """保存调度配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存调度配置失败: {e}")
    
    def _load_device_stats(self) -> Dict[str, Any]:
        """加载设备统计数据"""
        if os.path.exists(self.stats_file):
            try:
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                self.logger.info(f"设备统计数据加载成功，设备数: {len(stats.get('devices', {}))}")
                return stats
            except Exception as e:
                self.logger.warning(f"加载设备统计数据失败: {e}")
        
        return {
            'devices': {},
            'global_stats': {
                'total_tasks': 0,
                'successful_tasks': 0,
                'failed_tasks': 0,
                'avg_response_time': 0.0
            },
            'last_updated': datetime.now().isoformat()
        }
    
    def _save_device_stats(self):
        """保存设备统计数据"""
        try:
            self.device_stats['last_updated'] = datetime.now().isoformat()
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.device_stats, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存设备统计数据失败: {e}")
    
    def register_device(self, device_id: str, device_info: Dict[str, Any]):
        """注册设备"""
        with self._lock:
            if device_id not in self.device_stats['devices']:
                self.device_stats['devices'][device_id] = {
                    'device_info': device_info,
                    'total_tasks': 0,
                    'successful_tasks': 0,
                    'failed_tasks': 0,
                    'total_response_time': 0.0,
                    'avg_response_time': 0.0,
                    'success_rate': 1.0,
                    'performance_score': 1.0,
                    'last_used': None,
                    'consecutive_failures': 0,
                    'last_failure_time': None,
                    'availability_score': 1.0
                }
                self.logger.info(f"设备已注册: {device_id}")
    
    def select_best_device(self, available_devices: List[str], 
                          task_priority: str = 'normal') -> Optional[str]:
        """选择最佳设备"""
        if not available_devices:
            return None
        
        if len(available_devices) == 1:
            return available_devices[0]
        
        try:
            strategy = self.config.get('device_selection_strategy', 'performance_based')
            
            if strategy == 'performance_based':
                return self._select_by_performance(available_devices, task_priority)
            elif strategy == 'round_robin':
                return self._select_round_robin(available_devices)
            elif strategy == 'random':
                return random.choice(available_devices)
            else:
                return self._select_by_performance(available_devices, task_priority)
                
        except Exception as e:
            self.logger.error(f"设备选择失败: {e}")
            return available_devices[0]  # 返回第一个可用设备
    
    def _select_by_performance(self, available_devices: List[str], 
                              task_priority: str) -> str:
        """基于性能选择设备"""
        device_scores = {}
        
        for device_id in available_devices:
            score = self._calculate_device_score(device_id, task_priority)
            device_scores[device_id] = score
        
        # 选择得分最高的设备
        best_device = max(device_scores.items(), key=lambda x: x[1])
        
        self.logger.debug(f"设备选择结果: {best_device[0]} (得分: {best_device[1]:.3f})")
        return best_device[0]
    
    def _calculate_device_score(self, device_id: str, task_priority: str) -> float:
        """计算设备得分"""
        if device_id not in self.device_stats['devices']:
            return 0.5  # 新设备默认得分
        
        stats = self.device_stats['devices'][device_id]
        
        # 性能得分（基于成功率和响应时间）
        success_rate = stats.get('success_rate', 1.0)
        avg_response_time = stats.get('avg_response_time', 1.0)
        
        # 响应时间得分（越快越好）
        time_score = max(0.1, 1.0 / (1.0 + avg_response_time / 10.0))
        
        # 综合性能得分
        performance_score = (success_rate * 0.7 + time_score * 0.3)
        
        # 可用性得分（考虑最近的失败情况）
        availability_score = self._calculate_availability_score(stats)
        
        # 任务优先级调整
        priority_multiplier = self.priority_weights.get(task_priority, 1.0)
        
        # 最终得分
        final_score = (
            performance_score * self.config['performance_weight'] +
            availability_score * self.config['availability_weight']
        ) * priority_multiplier
        
        return max(self.config['min_device_score'], final_score)
    
    def _calculate_availability_score(self, stats: Dict[str, Any]) -> float:
        """计算设备可用性得分"""
        consecutive_failures = stats.get('consecutive_failures', 0)
        last_failure_time = stats.get('last_failure_time')
        
        # 基础可用性得分
        base_score = 1.0
        
        # 连续失败惩罚
        if consecutive_failures > 0:
            penalty = min(0.8, consecutive_failures * 0.2)
            base_score -= penalty
        
        # 最近失败时间惩罚
        if last_failure_time:
            try:
                failure_time = datetime.fromisoformat(last_failure_time)
                time_since_failure = datetime.now() - failure_time
                
                # 如果最近1分钟内失败，降低得分
                if time_since_failure < timedelta(minutes=1):
                    base_score *= 0.5
                elif time_since_failure < timedelta(minutes=5):
                    base_score *= 0.8
                    
            except Exception:
                pass
        
        return max(0.1, base_score)
    
    def _select_round_robin(self, available_devices: List[str]) -> str:
        """轮询选择设备"""
        # 简单的轮询实现
        if not hasattr(self, '_round_robin_index'):
            self._round_robin_index = 0
        
        device = available_devices[self._round_robin_index % len(available_devices)]
        self._round_robin_index += 1
        
        return device
    
    def get_retry_strategy(self, task_info: Dict[str, Any], 
                          attempt: int) -> Tuple[str, float]:
        """获取重试策略"""
        strategy_name = self.config.get('retry_strategy', 'adaptive')
        
        if strategy_name == 'adaptive':
            return self._get_adaptive_retry(task_info, attempt)
        elif strategy_name in self.retry_strategies:
            delays = self.retry_strategies[strategy_name]
            if attempt < len(delays):
                return strategy_name, delays[attempt]
            else:
                return strategy_name, delays[-1]  # 使用最后一个延迟值
        else:
            # 默认策略
            return 'exponential_backoff', min(2 ** attempt, 16)
    
    def _get_adaptive_retry(self, task_info: Dict[str, Any], 
                           attempt: int) -> Tuple[str, float]:
        """自适应重试策略"""
        base_delay = self.config.get('adaptive_retry_base', 1.5)
        max_delay = self.config.get('adaptive_retry_max', 30.0)
        
        # 基于任务类型和历史性能调整
        task_type = task_info.get('type', 'default')
        device_id = task_info.get('device_id')
        
        # 基础延迟
        delay = base_delay * (1.5 ** attempt)
        
        # 根据设备性能调整
        if device_id and device_id in self.device_stats['devices']:
            device_stats = self.device_stats['devices'][device_id]
            success_rate = device_stats.get('success_rate', 1.0)
            
            # 成功率低的设备增加延迟
            if success_rate < 0.5:
                delay *= 2.0
            elif success_rate < 0.8:
                delay *= 1.5
        
        # 根据全局性能调整
        global_success_rate = self._get_global_success_rate()
        if global_success_rate < 0.7:
            delay *= 1.3
        
        delay = min(delay, max_delay)
        
        return 'adaptive', delay
    
    def _get_global_success_rate(self) -> float:
        """获取全局成功率"""
        global_stats = self.device_stats['global_stats']
        total = global_stats.get('total_tasks', 0)
        successful = global_stats.get('successful_tasks', 0)
        
        if total == 0:
            return 1.0
        
        return successful / total
    
    def record_task_result(self, device_id: str, task_info: Dict[str, Any], 
                          success: bool, response_time: float, 
                          attempt: int = 1):
        """记录任务结果"""
        with self._lock:
            try:
                # 确保设备已注册
                if device_id not in self.device_stats['devices']:
                    self.register_device(device_id, {'id': device_id})
                
                device_stats = self.device_stats['devices'][device_id]
                global_stats = self.device_stats['global_stats']
                
                # 更新设备统计
                device_stats['total_tasks'] += 1
                device_stats['total_response_time'] += response_time
                device_stats['last_used'] = datetime.now().isoformat()
                
                if success:
                    device_stats['successful_tasks'] += 1
                    device_stats['consecutive_failures'] = 0
                    
                    # 成功奖励
                    current_score = device_stats.get('performance_score', 1.0)
                    bonus = self.config.get('success_bonus', 0.1)
                    device_stats['performance_score'] = min(1.0, current_score + bonus)
                    
                else:
                    device_stats['failed_tasks'] += 1
                    device_stats['consecutive_failures'] += 1
                    device_stats['last_failure_time'] = datetime.now().isoformat()
                    
                    # 失败惩罚
                    current_score = device_stats.get('performance_score', 1.0)
                    penalty = self.config.get('failure_penalty', 0.2)
                    device_stats['performance_score'] = max(0.1, current_score - penalty)
                
                # 重新计算统计指标
                device_stats['success_rate'] = (
                    device_stats['successful_tasks'] / device_stats['total_tasks']
                )
                device_stats['avg_response_time'] = (
                    device_stats['total_response_time'] / device_stats['total_tasks']
                )
                
                # 更新全局统计
                global_stats['total_tasks'] += 1
                if success:
                    global_stats['successful_tasks'] += 1
                else:
                    global_stats['failed_tasks'] += 1
                
                # 更新全局平均响应时间
                if global_stats['total_tasks'] > 0:
                    total_time = (global_stats.get('avg_response_time', 0.0) * 
                                (global_stats['total_tasks'] - 1) + response_time)
                    global_stats['avg_response_time'] = total_time / global_stats['total_tasks']
                
                # 记录性能历史
                self.performance_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'device_id': device_id,
                    'success': success,
                    'response_time': response_time,
                    'attempt': attempt
                })
                
                # 定期保存统计数据
                if device_stats['total_tasks'] % 10 == 0:
                    self._save_device_stats()
                
                self.logger.debug(f"任务结果已记录: {device_id}, 成功: {success}, "
                                f"响应时间: {response_time:.2f}s, 尝试次数: {attempt}")
                
            except Exception as e:
                self.logger.error(f"记录任务结果失败: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            devices = self.device_stats['devices']
            global_stats = self.device_stats['global_stats']
            
            # 设备排名
            device_rankings = []
            for device_id, stats in devices.items():
                device_rankings.append({
                    'device_id': device_id,
                    'success_rate': stats.get('success_rate', 0.0),
                    'avg_response_time': stats.get('avg_response_time', 0.0),
                    'performance_score': stats.get('performance_score', 0.0),
                    'total_tasks': stats.get('total_tasks', 0)
                })
            
            # 按性能得分排序
            device_rankings.sort(key=lambda x: x['performance_score'], reverse=True)
            
            # 最近性能趋势
            recent_performance = list(self.performance_history)[-20:]  # 最近20个任务
            recent_success_rate = 0.0
            if recent_performance:
                recent_successes = sum(1 for p in recent_performance if p['success'])
                recent_success_rate = recent_successes / len(recent_performance)
            
            return {
                'global_stats': global_stats,
                'device_count': len(devices),
                'device_rankings': device_rankings[:5],  # 前5名设备
                'recent_success_rate': recent_success_rate,
                'total_performance_records': len(self.performance_history),
                'recommendations': self._generate_recommendations()
            }
            
        except Exception as e:
            self.logger.error(f"生成性能报告失败: {e}")
            return {'error': str(e)}
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        try:
            global_stats = self.device_stats['global_stats']
            global_success_rate = self._get_global_success_rate()
            
            if global_success_rate < 0.8:
                recommendations.append("全局成功率偏低，建议检查设备状态和网络连接")
            
            if global_stats.get('avg_response_time', 0) > 10.0:
                recommendations.append("平均响应时间较长，建议优化任务处理逻辑")
            
            # 检查设备性能
            devices = self.device_stats['devices']
            poor_devices = [
                device_id for device_id, stats in devices.items()
                if stats.get('success_rate', 1.0) < 0.6
            ]
            
            if poor_devices:
                recommendations.append(f"设备 {', '.join(poor_devices)} 性能较差，建议检查或更换")
            
            if not recommendations:
                recommendations.append("系统运行良好，无需特别优化")
            
        except Exception as e:
            recommendations.append(f"生成建议时出错: {e}")
        
        return recommendations


def test_intelligent_scheduler():
    """测试智能调度器"""
    scheduler = IntelligentScheduler()
    
    # 注册测试设备
    devices = ['device_1', 'device_2', 'device_3']
    for device_id in devices:
        scheduler.register_device(device_id, {'id': device_id, 'type': 'test'})
    
    # 测试设备选择
    print("测试设备选择:")
    for i in range(5):
        selected = scheduler.select_best_device(devices, 'normal')
        print(f"选择的设备: {selected}")
        
        # 模拟任务执行
        success = random.choice([True, True, True, False])  # 75%成功率
        response_time = random.uniform(1.0, 5.0)
        
        scheduler.record_task_result(selected, {'type': 'test'}, success, response_time)
    
    # 测试重试策略
    print("\n测试重试策略:")
    task_info = {'type': 'test', 'device_id': 'device_1'}
    for attempt in range(1, 4):
        strategy, delay = scheduler.get_retry_strategy(task_info, attempt)
        print(f"尝试 {attempt}: 策略 {strategy}, 延迟 {delay:.2f}秒")
    
    # 获取性能报告
    print("\n性能报告:")
    report = scheduler.get_performance_report()
    print(json.dumps(report, indent=2, ensure_ascii=False))


if __name__ == '__main__':
    test_intelligent_scheduler()
