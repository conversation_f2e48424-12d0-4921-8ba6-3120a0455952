#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Local Phone Marker - 本地电话标记工具
基于ADB控制，支持本地标记记录和查询
"""

import subprocess
import time
import json
import sqlite3
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional
import os
import re

class LocalPhoneMarker:
    def __init__(self, db_path: str = "phone_marks.db"):
        self.db_path = db_path
        self.init_database()
        self.device_connected = self._check_device()
    
    def _check_device(self) -> bool:
        """检查设备连接状态"""
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            devices = result.stdout.strip().split('\n')[1:]
            connected_devices = [d for d in devices if d.strip() and 'device' in d]
            return len(connected_devices) > 0
        except Exception as e:
            print(f"ADB检查失败: {e}")
            return False
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建电话号码标记表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS phone_marks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone_number TEXT NOT NULL,
                mark_type TEXT NOT NULL,
                mark_count INTEGER DEFAULT 1,
                first_mark_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_mark_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT,
                UNIQUE(phone_number, mark_type)
            )
        ''')
        
        # 创建标记类型表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mark_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_name TEXT UNIQUE NOT NULL,
                type_color TEXT,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # 插入默认标记类型
        default_types = [
            ('骚扰电话', '#FF0000'),
            ('诈骗电话', '#FF6600'),
            ('推销电话', '#FFCC00'),
            ('快递送餐', '#00CC00'),
            ('客服电话', '#0066CC'),
            ('其他', '#999999')
        ]
        
        for type_name, color in default_types:
            cursor.execute('''
                INSERT OR IGNORE INTO mark_types (type_name, type_color) 
                VALUES (?, ?)
            ''', (type_name, color))
        
        conn.commit()
        conn.close()
    
    def add_mark(self, phone_number: str, mark_type: str, description: str = "") -> bool:
        """
        添加电话标记
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查是否已存在相同标记
            cursor.execute('''
                SELECT mark_count FROM phone_marks 
                WHERE phone_number = ? AND mark_type = ?
            ''', (phone_number, mark_type))
            
            result = cursor.fetchone()
            
            if result:
                # 更新现有记录
                cursor.execute('''
                    UPDATE phone_marks 
                    SET mark_count = mark_count + 1, 
                        last_mark_time = CURRENT_TIMESTAMP,
                        description = ?
                    WHERE phone_number = ? AND mark_type = ?
                ''', (description, phone_number, mark_type))
            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO phone_marks (phone_number, mark_type, description)
                    VALUES (?, ?, ?)
                ''', (phone_number, mark_type, description))
            
            conn.commit()
            conn.close()
            
            print(f"已标记 {phone_number} 为 {mark_type}")
            return True
            
        except Exception as e:
            print(f"添加标记失败: {e}")
            return False
    
    def query_mark(self, phone_number: str) -> List[Dict]:
        """
        查询电话号码标记
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT mark_type, mark_count, first_mark_time, last_mark_time, description
                FROM phone_marks 
                WHERE phone_number = ?
                ORDER BY mark_count DESC
            ''', (phone_number,))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    'phone_number': phone_number,
                    'mark_type': row[0],
                    'mark_count': row[1],
                    'first_mark_time': row[2],
                    'last_mark_time': row[3],
                    'description': row[4]
                })
            
            conn.close()
            return results
            
        except Exception as e:
            print(f"查询标记失败: {e}")
            return []
    
    def batch_query(self, phone_list: List[str]) -> List[Dict]:
        """
        批量查询电话号码标记
        """
        all_results = []
        
        for i, phone in enumerate(phone_list, 1):
            print(f"查询进度: {i}/{len(phone_list)} - {phone}")
            
            if not self._validate_phone(phone):
                print(f"无效号码: {phone}")
                continue
            
            results = self.query_mark(phone)
            if results:
                all_results.extend(results)
            else:
                # 添加无标记记录
                all_results.append({
                    'phone_number': phone,
                    'mark_type': '无标记',
                    'mark_count': 0,
                    'first_mark_time': '',
                    'last_mark_time': '',
                    'description': ''
                })
        
        return all_results
    
    def make_call_and_mark(self, phone_number: str, mark_type: str = None, duration: int = 5) -> bool:
        """
        拨打电话并标记
        """
        if not self.device_connected:
            print("设备未连接")
            return False
        
        try:
            print(f"正在拨打: {phone_number}")
            
            # 拨打电话
            subprocess.run(['adb', 'shell', 'am', 'start', '-a', 'android.intent.action.CALL', 
                          '-d', f'tel:{phone_number}'])
            time.sleep(3)
            
            # 等待通话建立
            time.sleep(duration)
            
            # 挂断电话
            subprocess.run(['adb', 'shell', 'input', 'keyevent', 'KEYCODE_ENDCALL'])
            time.sleep(2)
            
            # 如果指定了标记类型，自动添加标记
            if mark_type:
                self.add_mark(phone_number, mark_type, f"自动标记 - 通话时长{duration}秒")
            
            print(f"通话完成: {phone_number}")
            return True
            
        except Exception as e:
            print(f"拨号失败: {e}")
            return False
    
    def batch_call_and_mark(self, phone_list: List[str], mark_type: str = None, interval: int = 3) -> Dict[str, bool]:
        """
        批量拨打电话并标记
        """
        results = {}
        
        for i, phone in enumerate(phone_list, 1):
            print(f"进度: {i}/{len(phone_list)}")
            results[phone] = self.make_call_and_mark(phone, mark_type)
            
            if i < len(phone_list):
                print(f"等待 {interval} 秒...")
                time.sleep(interval)
        
        return results
    
    def get_call_log(self) -> List[Dict]:
        """获取通话记录"""
        try:
            result = subprocess.run(['adb', 'shell', 'content', 'query', '--uri', 'content://call_log/calls'], 
                                  capture_output=True, text=True)
            
            lines = result.stdout.strip().split('\n')
            call_logs = []
            
            for line in lines:
                if 'Row:' in line:
                    parts = line.split(',')
                    call_log = {}
                    for part in parts:
                        if '=' in part:
                            key, value = part.split('=', 1)
                            call_log[key.strip()] = value.strip()
                    call_logs.append(call_log)
            
            return call_logs
            
        except Exception as e:
            print(f"获取通话记录失败: {e}")
            return []
    
    def export_results(self, results: List[Dict], filename: str = None) -> str:
        """
        导出结果到Excel
        """
        if not filename:
            filename = f"phone_mark_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        df = pd.DataFrame(results)
        df['export_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        df.to_excel(filename, index=False)
        print(f"结果已导出到: {filename}")
        return filename
    
    def generate_statistics(self) -> Dict:
        """
        生成统计报告
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 总标记数
            cursor.execute('SELECT COUNT(*) FROM phone_marks')
            total_marks = cursor.fetchone()[0]
            
            # 标记类型分布
            cursor.execute('''
                SELECT mark_type, COUNT(*) as count, SUM(mark_count) as total_count
                FROM phone_marks 
                GROUP BY mark_type 
                ORDER BY total_count DESC
            ''')
            type_distribution = {}
            for row in cursor.fetchall():
                type_distribution[row[0]] = {
                    'unique_numbers': row[1],
                    'total_marks': row[2]
                }
            
            # 最近标记
            cursor.execute('''
                SELECT phone_number, mark_type, mark_count, last_mark_time
                FROM phone_marks 
                ORDER BY last_mark_time DESC 
                LIMIT 10
            ''')
            recent_marks = []
            for row in cursor.fetchall():
                recent_marks.append({
                    'phone_number': row[0],
                    'mark_type': row[1],
                    'mark_count': row[2],
                    'last_mark_time': row[3]
                })
            
            conn.close()
            
            return {
                'total_marks': total_marks,
                'type_distribution': type_distribution,
                'recent_marks': recent_marks,
                'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            print(f"生成统计报告失败: {e}")
            return {}
    
    def _validate_phone(self, phone: str) -> bool:
        """验证电话号码格式"""
        # 支持手机号和座机号
        mobile_pattern = r'^1[3-9]\d{9}$'
        landline_pattern = r'^0\d{2,3}-?\d{7,8}$'
        
        return bool(re.match(mobile_pattern, phone) or re.match(landline_pattern, phone))
    
    def interactive_mark(self):
        """
        交互式标记界面
        """
        print("\n=== 交互式电话标记 ===")
        
        while True:
            phone = input("\n请输入电话号码 (输入 'quit' 退出): ").strip()
            
            if phone.lower() == 'quit':
                break
            
            if not self._validate_phone(phone):
                print("无效的电话号码格式")
                continue
            
            # 查询现有标记
            existing_marks = self.query_mark(phone)
            if existing_marks:
                print(f"\n现有标记:")
                for mark in existing_marks:
                    print(f"  - {mark['mark_type']}: {mark['mark_count']} 次")
            
            # 选择标记类型
            print("\n标记类型:")
            mark_types = ['骚扰电话', '诈骗电话', '推销电话', '快递送餐', '客服电话', '其他']
            for i, mark_type in enumerate(mark_types, 1):
                print(f"  {i}. {mark_type}")
            
            try:
                choice = int(input("请选择标记类型 (1-6): "))
                if 1 <= choice <= 6:
                    mark_type = mark_types[choice - 1]
                    description = input("备注信息 (可选): ").strip()
                    
                    self.add_mark(phone, mark_type, description)
                else:
                    print("无效选择")
            except ValueError:
                print("请输入有效数字")

def main():
    """主函数"""
    marker = LocalPhoneMarker()
    
    if not marker.device_connected:
        print("请连接Android设备并开启USB调试")
        return
    
    print("=== 本地电话标记工具 ===")
    print("1. 交互式标记")
    print("2. 批量查询")
    print("3. 批量拨号标记")
    print("4. 生成统计报告")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择功能 (1-5): ").strip()
        
        if choice == '1':
            marker.interactive_mark()
        elif choice == '2':
            phones = input("请输入电话号码列表 (用逗号分隔): ").strip().split(',')
            phones = [p.strip() for p in phones if p.strip()]
            results = marker.batch_query(phones)
            marker.export_results(results)
        elif choice == '3':
            phones = input("请输入电话号码列表 (用逗号分隔): ").strip().split(',')
            phones = [p.strip() for p in phones if p.strip()]
            mark_type = input("请输入标记类型: ").strip()
            results = marker.batch_call_and_mark(phones, mark_type)
            print("批量拨号完成")
        elif choice == '4':
            stats = marker.generate_statistics()
            print("\n统计报告:")
            print(json.dumps(stats, indent=2, ensure_ascii=False))
        elif choice == '5':
            break
        else:
            print("无效选择")

if __name__ == "__main__":
    main() 