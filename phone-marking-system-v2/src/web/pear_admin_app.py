"""
Pear Admin 风格的Web管理系统
基于FastAPI + Jinja2模板引擎，集成Pear Admin UI组件
提供现代化的后台管理界面
"""

import os
import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path

from fastapi import FastAPI, HTTPException, Depends, Request, Form, File, UploadFile, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from pydantic import BaseModel, Field
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PearAdminApp:
    """Pear Admin 风格的Web应用"""
    
    def __init__(self):
        self.app = FastAPI(
            title="电话号码标记识别管理系统",
            description="基于Pear Admin风格的现代化后台管理系统",
            version="2.0.0"
        )
        
        # 设置路径
        self.base_dir = Path(__file__).parent
        self.static_dir = self.base_dir / "static"
        self.templates_dir = self.base_dir / "templates"
        
        # 确保目录存在
        self.static_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)
        
        # 初始化模板引擎
        self.templates = Jinja2Templates(directory=str(self.templates_dir))
        
        # 配置应用
        self._setup_middleware()
        self._setup_static_files()
        self._setup_routes()
        
    def _setup_middleware(self):
        """配置中间件"""
        # CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
    def _setup_static_files(self):
        """配置静态文件"""
        # 挂载静态文件目录
        self.app.mount("/static", StaticFiles(directory=str(self.static_dir)), name="static")
        
    def _setup_routes(self):
        """配置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard(request: Request):
            """仪表板首页"""
            context = {
                "request": request,
                "title": "仪表板",
                "user": {"name": "管理员", "role": "admin"},
                "stats": {
                    "total_phones": 12580,
                    "processed_today": 1250,
                    "success_rate": 98.5,
                    "active_devices": 3
                }
            }
            return self.templates.TemplateResponse("dashboard.html", context)
        
        @self.app.get("/login", response_class=HTMLResponse)
        async def login_page(request: Request):
            """登录页面"""
            context = {
                "request": request,
                "title": "用户登录"
            }
            return self.templates.TemplateResponse("login.html", context)
        
        @self.app.post("/api/login")
        async def login(username: str = Form(...), password: str = Form(...)):
            """用户登录API"""
            # 简单的登录验证（实际应用中应该使用数据库验证）
            if username == "admin" and password == "admin":
                return {"status": "success", "message": "登录成功", "token": "fake-jwt-token"}
            else:
                raise HTTPException(status_code=401, detail="用户名或密码错误")
        
        @self.app.get("/data/import", response_class=HTMLResponse)
        async def data_import_page(request: Request):
            """数据导入页面"""
            context = {
                "request": request,
                "title": "数据导入",
                "user": {"name": "管理员", "role": "admin"}
            }
            return self.templates.TemplateResponse("data_import.html", context)
        
        @self.app.post("/api/data/import")
        async def import_data(file: UploadFile = File(...)):
            """数据导入API"""
            if not file.filename.endswith(('.xlsx', '.xls', '.csv')):
                raise HTTPException(status_code=400, detail="不支持的文件格式")
            
            # 这里应该实现实际的数据导入逻辑
            return {
                "status": "success",
                "message": f"文件 {file.filename} 导入成功",
                "task_id": "task_123456"
            }
        
        @self.app.get("/data/export", response_class=HTMLResponse)
        async def data_export_page(request: Request):
            """数据导出页面"""
            context = {
                "request": request,
                "title": "数据导出",
                "user": {"name": "管理员", "role": "admin"}
            }
            return self.templates.TemplateResponse("data_export.html", context)
        
        @self.app.get("/stats", response_class=HTMLResponse)
        async def stats_page(request: Request):
            """统计分析页面"""
            context = {
                "request": request,
                "title": "统计分析",
                "user": {"name": "管理员", "role": "admin"}
            }
            return self.templates.TemplateResponse("stats.html", context)
        
        @self.app.get("/devices", response_class=HTMLResponse)
        async def devices_page(request: Request):
            """设备管理页面"""
            context = {
                "request": request,
                "title": "设备管理",
                "user": {"name": "管理员", "role": "admin"},
                "devices": [
                    {"id": 1, "name": "设备1", "status": "在线", "last_seen": "2025-07-01 11:00:00"},
                    {"id": 2, "name": "设备2", "status": "离线", "last_seen": "2025-07-01 10:30:00"},
                    {"id": 3, "name": "设备3", "status": "在线", "last_seen": "2025-07-01 11:15:00"}
                ]
            }
            return self.templates.TemplateResponse("devices.html", context)
        
        @self.app.get("/users", response_class=HTMLResponse)
        async def users_page(request: Request):
            """用户管理页面"""
            context = {
                "request": request,
                "title": "用户管理",
                "user": {"name": "管理员", "role": "admin"}
            }
            return self.templates.TemplateResponse("users.html", context)
        
        @self.app.get("/logs", response_class=HTMLResponse)
        async def logs_page(request: Request):
            """日志查看页面"""
            context = {
                "request": request,
                "title": "系统日志",
                "user": {"name": "管理员", "role": "admin"}
            }
            return self.templates.TemplateResponse("logs.html", context)
        
        @self.app.get("/api/stats/overview")
        async def get_stats_overview():
            """获取统计概览数据"""
            return {
                "total_phones": 12580,
                "processed_today": 1250,
                "success_rate": 98.5,
                "active_devices": 3,
                "chart_data": {
                    "labels": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                    "datasets": [{
                        "label": "处理数量",
                        "data": [1200, 1900, 3000, 5000, 2000, 3000, 1250],
                        "backgroundColor": "rgba(54, 162, 235, 0.2)",
                        "borderColor": "rgba(54, 162, 235, 1)"
                    }]
                }
            }
        
        @self.app.get("/api/devices/status")
        async def get_devices_status():
            """获取设备状态"""
            return {
                "devices": [
                    {"id": 1, "name": "设备1", "status": "online", "cpu": 45, "memory": 60},
                    {"id": 2, "name": "设备2", "status": "offline", "cpu": 0, "memory": 0},
                    {"id": 3, "name": "设备3", "status": "online", "cpu": 30, "memory": 40}
                ]
            }
    
    def run(self, host: str = "127.0.0.1", port: int = 8080, debug: bool = True):
        """运行应用"""
        uvicorn.run(self.app, host=host, port=port, reload=debug)


# 创建应用实例
pear_admin_app = PearAdminApp()

if __name__ == "__main__":
    pear_admin_app.run()
