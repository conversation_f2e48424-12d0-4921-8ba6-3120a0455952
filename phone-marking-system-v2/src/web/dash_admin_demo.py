"""
Dash-FastAPI-Admin 集成演示
展示如何将电话标记系统的核心功能集成到 Dash 管理界面中
"""

import dash
from dash import dcc, html, Input, Output, State, callback_context
import feffery_antd_components as fac
import feffery_utils_components as fuc
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import json

# 初始化 Dash 应用
app = dash.Dash(__name__)

# 模拟数据
MOCK_PHONE_DATA = [
    {"id": 1, "phone_number": "13800138000", "province": "北京", "city": "北京", "mark_count": 15, "status": "completed"},
    {"id": 2, "phone_number": "13900139000", "province": "上海", "city": "上海", "mark_count": 8, "status": "completed"},
    {"id": 3, "phone_number": "13700137000", "province": "广东", "city": "广州", "mark_count": 22, "status": "processing"},
    {"id": 4, "phone_number": "13600136000", "province": "浙江", "city": "杭州", "mark_count": 5, "status": "pending"},
]

MOCK_TASKS = [
    {"id": 1, "task_name": "批量导入-20250701", "total_count": 1000, "processed_count": 850, "status": "running"},
    {"id": 2, "task_name": "批量导入-20250630", "total_count": 500, "processed_count": 500, "status": "completed"},
]

MOCK_DEVICES = [
    {"id": 1, "device_name": "测试设备1", "device_id": "device_001", "status": "online", "last_heartbeat": "2025-07-01 11:30:00"},
    {"id": 2, "device_name": "测试设备2", "device_id": "device_002", "status": "offline", "last_heartbeat": "2025-07-01 10:15:00"},
]

# 应用布局
app.layout = fac.AntdConfigProvider(
    fac.AntdLayout([
        # 顶部导航
        fac.AntdHeader([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdTitle("电话号码标记识别管理系统", level=3, style={"color": "white", "margin": 0})
                ], span=12),
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdAvatar(icon="antd-user", style={"backgroundColor": "#1890ff"}),
                        fac.AntdText("管理员", style={"color": "white"})
                    ], align="center")
                ], span=12, style={"textAlign": "right"})
            ])
        ], style={"backgroundColor": "#001529", "padding": "0 24px"}),
        
        fac.AntdLayout([
            # 侧边菜单
            fac.AntdSider([
                fac.AntdMenu(
                    id="main-menu",
                    menuItems=[
                        {"component": "Item", "props": {"key": "dashboard", "icon": "antd-dashboard", "title": "仪表板"}},
                        {"component": "SubMenu", "props": {"key": "data", "icon": "antd-database", "title": "数据管理"}, "children": [
                            {"component": "Item", "props": {"key": "import", "title": "批量导入"}},
                            {"component": "Item", "props": {"key": "export", "title": "数据导出"}},
                        ]},
                        {"component": "Item", "props": {"key": "stats", "icon": "antd-bar-chart", "title": "统计分析"}},
                        {"component": "Item", "props": {"key": "devices", "icon": "antd-mobile", "title": "设备管理"}},
                        {"component": "Item", "props": {"key": "tasks", "icon": "antd-clock-circle", "title": "任务监控"}},
                    ],
                    mode="inline",
                    defaultSelectedKeys=["dashboard"],
                    style={"height": "100%"}
                )
            ], width=256, style={"backgroundColor": "#f0f2f5"}),
            
            # 主内容区
            fac.AntdContent([
                html.Div(id="page-content", style={"padding": "24px"})
            ])
        ])
    ])
)

# 仪表板页面
def create_dashboard():
    return fac.AntdSpace([
        # 统计卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(title="总号码数", value=12580, prefix=fac.AntdIcon(icon="antd-phone"))
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(title="今日处理", value=1250, prefix=fac.AntdIcon(icon="antd-check-circle"))
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(title="成功率", value=98.5, suffix="%", prefix=fac.AntdIcon(icon="antd-rise"))
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(title="在线设备", value=3, prefix=fac.AntdIcon(icon="antd-mobile"))
                ])
            ], span=6),
        ], gutter=16),
        
        # 图表区域
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        figure=px.line(
                            x=["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                            y=[1200, 1900, 3000, 5000, 2000, 3000, 1250],
                            title="本周处理趋势"
                        )
                    )
                ], title="处理趋势")
            ], span=12),
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        figure=px.pie(
                            values=[30, 25, 20, 15, 10],
                            names=["北京", "上海", "广东", "浙江", "其他"],
                            title="省份分布"
                        )
                    )
                ], title="地区分布")
            ], span=12),
        ], gutter=16)
    ], direction="vertical", style={"width": "100%"})

# 批量导入页面
def create_import_page():
    return fac.AntdSpace([
        fac.AntdCard([
            fac.AntdUpload(
                id="phone-upload",
                apiUrl="/api/upload",  # 这里需要连接到实际的API
                fileMaxSize=50,
                fileTypes=["xlsx", "xls", "csv"],
                multiple=False,
                text="点击或拖拽文件到此区域上传",
                hint="支持上万条号码批量导入，支持Excel和CSV格式"
            ),
            fac.AntdDivider(),
            fac.AntdProgress(id="upload-progress", percent=0, status="normal"),
        ], title="文件上传"),
        
        fac.AntdCard([
            fac.AntdTable(
                id="phone-data-table",
                columns=[
                    {"title": "电话号码", "dataIndex": "phone_number", "key": "phone_number"},
                    {"title": "省份", "dataIndex": "province", "key": "province"},
                    {"title": "城市", "dataIndex": "city", "key": "city"},
                    {"title": "标记数量", "dataIndex": "mark_count", "key": "mark_count"},
                    {"title": "状态", "dataIndex": "status", "key": "status"},
                ],
                data=MOCK_PHONE_DATA,
                pagination={"pageSize": 10}
            )
        ], title="导入数据")
    ], direction="vertical", style={"width": "100%"})

# 设备管理页面
def create_devices_page():
    return fac.AntdSpace([
        fac.AntdCard([
            fac.AntdSpace([
                fac.AntdButton("刷新设备", id="refresh-devices", type="primary", icon="antd-reload"),
                fac.AntdButton("添加设备", id="add-device", icon="antd-plus"),
            ])
        ]),
        
        fac.AntdCard([
            fac.AntdTable(
                id="devices-table",
                columns=[
                    {"title": "设备名称", "dataIndex": "device_name", "key": "device_name"},
                    {"title": "设备ID", "dataIndex": "device_id", "key": "device_id"},
                    {"title": "状态", "dataIndex": "status", "key": "status"},
                    {"title": "最后心跳", "dataIndex": "last_heartbeat", "key": "last_heartbeat"},
                    {"title": "操作", "dataIndex": "actions", "key": "actions"},
                ],
                data=MOCK_DEVICES,
                pagination={"pageSize": 10}
            )
        ], title="设备列表")
    ], direction="vertical", style={"width": "100%"})

# 任务监控页面
def create_tasks_page():
    return fac.AntdSpace([
        fac.AntdCard([
            fac.AntdTable(
                id="tasks-table",
                columns=[
                    {"title": "任务名称", "dataIndex": "task_name", "key": "task_name"},
                    {"title": "总数量", "dataIndex": "total_count", "key": "total_count"},
                    {"title": "已处理", "dataIndex": "processed_count", "key": "processed_count"},
                    {"title": "进度", "dataIndex": "progress", "key": "progress"},
                    {"title": "状态", "dataIndex": "status", "key": "status"},
                ],
                data=[
                    {**task, "progress": f"{task['processed_count']}/{task['total_count']} ({task['processed_count']/task['total_count']*100:.1f}%)"}
                    for task in MOCK_TASKS
                ],
                pagination={"pageSize": 10}
            )
        ], title="任务列表")
    ], direction="vertical", style={"width": "100%"})

# 统计分析页面
def create_stats_page():
    return fac.AntdSpace([
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        figure=px.bar(
                            x=["北京", "上海", "广东", "浙江", "江苏"],
                            y=[1500, 1200, 2200, 800, 600],
                            title="各省份号码数量"
                        )
                    )
                ], title="地区统计")
            ], span=12),
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        figure=px.line(
                            x=pd.date_range("2025-06-25", "2025-07-01"),
                            y=[100, 150, 200, 180, 220, 250, 300],
                            title="标记数量增长趋势"
                        )
                    )
                ], title="增长趋势")
            ], span=12),
        ], gutter=16)
    ], direction="vertical", style={"width": "100%"})

# 页面路由回调
@app.callback(
    Output("page-content", "children"),
    Input("main-menu", "currentKey")
)
def update_page_content(current_key):
    if current_key == "dashboard":
        return create_dashboard()
    elif current_key == "import":
        return create_import_page()
    elif current_key == "devices":
        return create_devices_page()
    elif current_key == "tasks":
        return create_tasks_page()
    elif current_key == "stats":
        return create_stats_page()
    else:
        return create_dashboard()

if __name__ == "__main__":
    app.run_server(debug=True, host="127.0.0.1", port=8088)
