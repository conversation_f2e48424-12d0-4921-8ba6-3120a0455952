/*
为电话标记系统添加菜单项
*/

USE `dash-fastapi`;

-- 插入电话标记系统的主菜单
INSERT INTO `sys_menu` VALUES 
-- 电话标记系统主菜单
(2000, '电话标记系统', 0, 1, 'phone-system', NULL, '', 1, 0, 'M', '0', '0', '', 'phone', 'admin', NOW(), '', NULL, '电话号码标记识别管理系统'),

-- 数据管理子菜单
(2001, '号码数据', 2000, 1, 'phone-data', 'phone-system/phone-data/index', '', 1, 0, 'C', '0', '0', 'phone:data:list', 'database', 'admin', NOW(), '', NULL, '电话号码数据管理'),
(2002, '批量导入', 2000, 2, 'batch-import', 'phone-system/batch-import/index', '', 1, 0, 'C', '0', '0', 'phone:import:upload', 'upload', 'admin', NOW(), '', NULL, '批量导入电话号码'),
(2003, '数据导出', 2000, 3, 'data-export', 'phone-system/data-export/index', '', 1, 0, 'C', '0', '0', 'phone:export:download', 'download', 'admin', NOW(), '', NULL, '数据导出功能'),
(2004, '数据统计', 2000, 4, 'data-statistics', 'phone-system/data-statistics/index', '', 1, 0, 'C', '0', '0', 'phone:statistics:view', 'bar-chart', 'admin', NOW(), '', NULL, '数据统计分析'),

-- 任务管理子菜单
(2010, '任务监控', 2000, 5, 'task-monitor', 'phone-system/task-monitor/index', '', 1, 0, 'C', '0', '0', 'phone:task:monitor', 'monitor', 'admin', NOW(), '', NULL, '批量任务监控'),
(2011, '任务历史', 2000, 6, 'task-history', 'phone-system/task-history/index', '', 1, 0, 'C', '0', '0', 'phone:task:history', 'history', 'admin', NOW(), '', NULL, '历史任务查询'),

-- 设备管理子菜单
(2020, '设备管理', 2000, 7, 'device-management', 'phone-system/device-management/index', '', 1, 0, 'C', '0', '0', 'phone:device:manage', 'mobile', 'admin', NOW(), '', NULL, '设备连接管理'),
(2021, '设备监控', 2000, 8, 'device-monitor', 'phone-system/device-monitor/index', '', 1, 0, 'C', '0', '0', 'phone:device:monitor', 'dashboard', 'admin', NOW(), '', NULL, '设备状态监控'),

-- 系统配置子菜单
(2030, '系统配置', 2000, 9, 'system-config', 'phone-system/system-config/index', '', 1, 0, 'C', '0', '0', 'phone:config:manage', 'setting', 'admin', NOW(), '', NULL, '系统参数配置'),
(2031, '数据同步', 2000, 10, 'data-sync', 'phone-system/data-sync/index', '', 1, 0, 'C', '0', '0', 'phone:sync:manage', 'sync', 'admin', NOW(), '', NULL, '本地线上数据同步');

-- 为超级管理员角色分配电话标记系统的所有权限
INSERT INTO `sys_role_menu` VALUES 
(1, 2000), (1, 2001), (1, 2002), (1, 2003), (1, 2004),
(1, 2010), (1, 2011), (1, 2020), (1, 2021), (1, 2030), (1, 2031);

-- 为操作员角色分配部分权限（除了系统配置）
INSERT INTO `sys_role_menu` VALUES 
(2, 2000), (2, 2001), (2, 2002), (2, 2003), (2, 2004),
(2, 2010), (2, 2011), (2, 2020), (2, 2021), (2, 2031);
