2025-07-01 11:47:40.641 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:47:41.062 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:47:41.064 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:47:41.066 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:47:41.317 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:47:55.192 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:47:55.261 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:48:02.980 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:48:03.319 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:48:03.320 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:48:03.323 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:48:03.400 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:48:07.991 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:48:08.056 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:48:22.240 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:48:22.295 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:49:07.768 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:49:07.823 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:49:09.571 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:49:09.618 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:49:09.996 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:49:10.061 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:49:10.374 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:49:10.427 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:49:10.904 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:49:10.961 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:55:11.975 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:55:12.323 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:55:12.325 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:55:12.328 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:55:12.424 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:55:36.855 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:55:36.855 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:55:36.859 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:55:37.457 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:55:47.904 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:55:47.985 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:55:48.951 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:55:48.995 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:55:54.547 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:55:54.548 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:55:54.555 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:55:54.629 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:01.953 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:02.013 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:02.853 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:02.904 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:03.165 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:03.215 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:03.873 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:03.924 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:06.430 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:56:06.431 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:56:06.447 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:56:06.512 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:10.825 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:10.881 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:24.027 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:24.211 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:24.460 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:56:24.461 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:56:24.465 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:56:24.530 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:26.018 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:26.090 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:26.787 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:26.841 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:27.559 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:27.623 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:41.481 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:41.561 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:42.214 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:42.264 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:54.416 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:56:54.418 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:56:54.437 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:56:54.505 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:56.531 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:56.581 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:57.118 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:57.169 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:57.328 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:57.372 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:56:57.697 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:56:57.745 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:15.425 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:57:15.428 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:57:15.436 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:57:15.545 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:18.036 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:57:18.083 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:18.522 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:57:18.576 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:18.756 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:57:18.804 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:42.449 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:57:42.449 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:57:42.453 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:57:42.529 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:45.130 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:57:45.175 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:45.691 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:57:45.735 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:46.408 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:57:46.461 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:58.068 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:57:58.069 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:57:58.072 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:57:58.140 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:57:59.807 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:57:59.853 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:58:00.483 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:00.534 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:58:00.685 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:00.730 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:58:01.215 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:01.270 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:58:34.258 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:58:34.260 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:58:34.264 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:58:34.355 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:58:34.905 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:34.957 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:58:35.437 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:35.583 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:58:35.651 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:35.698 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:58:36.020 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:58:36.074 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:59:09.394 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:59:09.395 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:59:09.399 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:59:09.473 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:59:09.945 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:59:09.999 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:59:10.290 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:59:10.337 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:59:11.025 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:59:11.070 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:59:54.441 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:59:54.786 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.index.skinName||请求参数:||请求结果:获取成功
2025-07-01 11:59:54.787 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.captchaEnabled||请求参数:||请求结果:获取成功
2025-07-01 11:59:54.790 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:get||请求Api:/common/config/query/sys.account.forgetUser||请求参数:||请求结果:获取成功
2025-07-01 11:59:54.863 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
2025-07-01 11:59:56.054 | ERROR    | utils.request:api_request:64 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/login/loginByAccount||请求结果:Expecting value: line 1 column 1 (char 0)
2025-07-01 11:59:56.109 | INFO     | utils.request:api_request:50 - [api]请求人:None||请求IP:127.0.0.1||请求方法:post||请求Api:/captcha/captchaImage||请求参数:||请求结果:获取验证码成功
