"""
数据统计分析页面
提供详细的数据分析、趋势图表、报表生成等功能
"""
import dash
from dash import html, dcc, Input, Output, State, callback
import feffery_antd_components as fac
import feffery_antd_charts as fact
import feffery_utils_components as fuc
from datetime import datetime, timedelta
import pandas as pd

from api.phone_data import get_phone_statistics_api


def render_data_statistics():
    """
    渲染数据统计分析页面
    """
    return html.Div([
        # 页面标题
        fac.AntdPageHeader(
            title='数据统计分析',
            subTitle='全面的数据分析和可视化报表',
            ghost=False
        ),
        
        # 时间范围选择器
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdText('时间范围：'),
                        fac.AntdDatePicker(
                            id='date-range-picker',
                            picker='range',
                            value=[
                                (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
                                datetime.now().strftime('%Y-%m-%d')
                            ],
                            style={'width': 300}
                        ),
                        fac.AntdButton(
                            '查询',
                            id='query-stats-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-search')
                        ),
                        fac.AntdButton(
                            '导出报表',
                            id='export-report-btn',
                            icon=fac.AntdIcon(icon='antd-download')
                        )
                    ])
                ], span=24)
            ])
        ], size='small', style={'marginBottom': 24}),
        
        # 核心指标卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总处理号码',
                        value=0,
                        valueStyle={'color': '#1890ff'},
                        prefix=fac.AntdIcon(icon='antd-phone'),
                        suffix='个',
                        id='total-processed-stat'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='识别准确率',
                        value=0,
                        valueStyle={'color': '#52c41a'},
                        prefix=fac.AntdIcon(icon='antd-check-circle'),
                        suffix='%',
                        precision=2,
                        id='accuracy-rate-stat'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='标记增长',
                        value=0,
                        valueStyle={'color': '#faad14'},
                        prefix=fac.AntdIcon(icon='antd-rise'),
                        suffix='个',
                        id='mark-growth-stat'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='平均处理时间',
                        value=0,
                        valueStyle={'color': '#722ed1'},
                        prefix=fac.AntdIcon(icon='antd-clock-circle'),
                        suffix='秒',
                        precision=1,
                        id='avg-process-time-stat'
                    )
                ], hoverable=True)
            ], span=6)
        ], gutter=16, style={'marginBottom': 24}),
        
        # 图表分析区域
        fac.AntdRow([
            # 处理量趋势图
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='号码处理趋势'),
                    fac.AntdTabs([
                        fac.AntdTabPane(
                            html.Div(id='daily-trend-chart', style={'height': 350}),
                            tab='按日统计',
                            key='daily'
                        ),
                        fac.AntdTabPane(
                            html.Div(id='hourly-trend-chart', style={'height': 350}),
                            tab='按小时统计',
                            key='hourly'
                        )
                    ], id='trend-tabs', activeKey='daily')
                ], title='处理趋势分析')
            ], span=12),
            
            # 标记类型分布
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='标记类型分布'),
                    html.Div(id='mark-type-distribution-chart', style={'height': 350})
                ], title='标记分类统计')
            ], span=12)
        ], gutter=16, style={'marginBottom': 24}),
        
        # 地域分布和运营商分析
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='地域分布'),
                    html.Div(id='region-distribution-chart', style={'height': 350})
                ], title='地域分析')
            ], span=12),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='运营商分布'),
                    html.Div(id='operator-distribution-chart', style={'height': 350})
                ], title='运营商统计')
            ], span=12)
        ], gutter=16, style={'marginBottom': 24}),
        
        # 详细数据表格
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdSelect(
                            id='table-view-select',
                            options=[
                                {'label': '按日汇总', 'value': 'daily'},
                                {'label': '按省份汇总', 'value': 'province'},
                                {'label': '按标记类型汇总', 'value': 'mark_type'},
                                {'label': '按运营商汇总', 'value': 'operator'}
                            ],
                            value='daily',
                            style={'width': 150}
                        ),
                        fac.AntdButton(
                            '刷新数据',
                            id='refresh-table-btn',
                            icon=fac.AntdIcon(icon='antd-reload')
                        )
                    ])
                ], span=24, style={'marginBottom': 16})
            ]),
            
            fac.AntdTable(
                id='statistics-detail-table',
                columns=[],
                data=[],
                pagination={
                    'pageSize': 10,
                    'current': 1,
                    'showSizeChanger': True,
                    'showQuickJumper': True,
                    'showTotal': True
                },
                bordered=True,
                size='small'
            )
        ], title='详细统计数据'),
        
        # 高级分析
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='标记增长分析'),
                    html.Div([
                        fac.AntdAlert(
                            message='号码标记增长趋势',
                            description='分析号码标记数量的变化趋势，识别异常增长模式',
                            type='info',
                            showIcon=True,
                            style={'marginBottom': 16}
                        ),
                        html.Div(id='mark-growth-analysis-chart', style={'height': 300})
                    ])
                ], title='增长趋势分析')
            ], span=12),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='识别准确率分析'),
                    html.Div([
                        fac.AntdAlert(
                            message='识别准确率监控',
                            description='监控系统识别准确率变化，优化识别算法',
                            type='success',
                            showIcon=True,
                            style={'marginBottom': 16}
                        ),
                        html.Div(id='accuracy-analysis-chart', style={'height': 300})
                    ])
                ], title='准确率监控')
            ], span=12)
        ], gutter=16, style={'marginTop': 24}),
        
        # 消息提示容器
        html.Div(id='statistics-message-container')
    ])


# 查询统计数据回调
@callback(
    [Output('total-processed-stat', 'value'),
     Output('accuracy-rate-stat', 'value'),
     Output('mark-growth-stat', 'value'),
     Output('avg-process-time-stat', 'value'),
     Output('daily-trend-chart', 'children'),
     Output('hourly-trend-chart', 'children'),
     Output('mark-type-distribution-chart', 'children'),
     Output('region-distribution-chart', 'children'),
     Output('operator-distribution-chart', 'children'),
     Output('mark-growth-analysis-chart', 'children'),
     Output('accuracy-analysis-chart', 'children')],
    [Input('query-stats-btn', 'n_clicks')],
    [State('date-range-picker', 'value')],
    prevent_initial_call=True
)
def query_statistics_data(n_clicks, date_range):
    """
    查询统计数据
    """
    if not n_clicks:
        return dash.no_update
    
    try:
        # 构建查询参数
        query_params = {}
        if date_range and len(date_range) == 2:
            query_params['start_date'] = date_range[0]
            query_params['end_date'] = date_range[1]
        
        # 调用API获取统计数据
        response = get_phone_statistics_api(query_params)
        
        # 模拟数据（实际应该从API获取）
        total_processed = 125680
        accuracy_rate = 94.5
        mark_growth = 2580
        avg_process_time = 1.2
        
        # 日趋势数据
        daily_data = [
            {'date': '2025-06-25', 'processed': 1200, 'success': 1140},
            {'date': '2025-06-26', 'processed': 1350, 'success': 1280},
            {'date': '2025-06-27', 'processed': 1180, 'success': 1115},
            {'date': '2025-06-28', 'processed': 1420, 'success': 1342},
            {'date': '2025-06-29', 'processed': 1380, 'success': 1305},
            {'date': '2025-06-30', 'processed': 1500, 'success': 1418},
            {'date': '2025-07-01', 'processed': 1250, 'success': 1181}
        ]
        
        daily_chart = fact.AntdLine(
            data=daily_data,
            xField='date',
            yField='processed',
            seriesField='type',
            smooth=True,
            color='#1890ff',
            point={'size': 5, 'shape': 'diamond'}
        )
        
        # 小时趋势数据
        hourly_data = [
            {'hour': f'{i:02d}:00', 'count': 50 + i * 10 + (i % 3) * 20}
            for i in range(24)
        ]
        
        hourly_chart = fact.AntdColumn(
            data=hourly_data,
            xField='hour',
            yField='count',
            color='#52c41a'
        )
        
        # 标记类型分布
        mark_type_data = [
            {'type': '正常号码', 'count': 85000, 'percentage': 67.6},
            {'type': '骚扰电话', 'count': 28000, 'percentage': 22.3},
            {'type': '诈骗电话', 'count': 9800, 'percentage': 7.8},
            {'type': '推销电话', 'count': 2880, 'percentage': 2.3}
        ]
        
        mark_type_chart = fact.AntdPie(
            data=mark_type_data,
            angleField='count',
            colorField='type',
            radius=0.8,
            label={'type': 'outer', 'content': '{name}: {percentage}%'},
            color=['#52c41a', '#faad14', '#ff4d4f', '#722ed1']
        )
        
        # 地域分布
        region_data = [
            {'province': '北京', 'count': 15680},
            {'province': '上海', 'count': 12450},
            {'province': '广东', 'count': 18920},
            {'province': '浙江', 'count': 9870},
            {'province': '江苏', 'count': 11230},
            {'province': '山东', 'count': 8760},
            {'province': '四川', 'count': 7650},
            {'province': '湖北', 'count': 6540}
        ]
        
        region_chart = fact.AntdColumn(
            data=region_data,
            xField='province',
            yField='count',
            color='#1890ff'
        )
        
        # 运营商分布
        operator_data = [
            {'operator': '中国移动', 'count': 52000},
            {'operator': '中国联通', 'count': 38000},
            {'operator': '中国电信', 'count': 35680}
        ]
        
        operator_chart = fact.AntdPie(
            data=operator_data,
            angleField='count',
            colorField='operator',
            radius=0.8,
            label={'type': 'outer', 'content': '{name}: {value}'},
            color=['#1890ff', '#52c41a', '#faad14']
        )
        
        # 标记增长分析
        growth_data = [
            {'date': '2025-06-01', 'growth': 120},
            {'date': '2025-06-08', 'growth': 135},
            {'date': '2025-06-15', 'growth': 118},
            {'date': '2025-06-22', 'growth': 142},
            {'date': '2025-06-29', 'growth': 158}
        ]
        
        growth_chart = fact.AntdArea(
            data=growth_data,
            xField='date',
            yField='growth',
            smooth=True,
            color='#faad14'
        )
        
        # 准确率分析
        accuracy_data = [
            {'date': '2025-06-01', 'accuracy': 93.2},
            {'date': '2025-06-08', 'accuracy': 94.1},
            {'date': '2025-06-15', 'accuracy': 93.8},
            {'date': '2025-06-22', 'accuracy': 94.5},
            {'date': '2025-06-29', 'accuracy': 94.8}
        ]
        
        accuracy_chart = fact.AntdLine(
            data=accuracy_data,
            xField='date',
            yField='accuracy',
            smooth=True,
            color='#52c41a',
            point={'size': 5, 'shape': 'circle'}
        )
        
        return (
            total_processed,
            accuracy_rate,
            mark_growth,
            avg_process_time,
            daily_chart,
            hourly_chart,
            mark_type_chart,
            region_chart,
            operator_chart,
            growth_chart,
            accuracy_chart
        )
        
    except Exception as e:
        # 错误处理，返回默认值
        return (0, 0, 0, 0, html.Div(), html.Div(), html.Div(), html.Div(), html.Div(), html.Div(), html.Div())


# 更新详细数据表格
@callback(
    [Output('statistics-detail-table', 'columns'),
     Output('statistics-detail-table', 'data')],
    [Input('table-view-select', 'value'),
     Input('refresh-table-btn', 'n_clicks')],
    prevent_initial_call=True
)
def update_detail_table(view_type, refresh_clicks):
    """
    更新详细数据表格
    """
    try:
        if view_type == 'daily':
            columns = [
                {'title': '日期', 'dataIndex': 'date', 'key': 'date'},
                {'title': '处理数量', 'dataIndex': 'processed', 'key': 'processed'},
                {'title': '成功数量', 'dataIndex': 'success', 'key': 'success'},
                {'title': '失败数量', 'dataIndex': 'failed', 'key': 'failed'},
                {'title': '成功率', 'dataIndex': 'success_rate', 'key': 'success_rate'}
            ]
            
            data = [
                {
                    'key': '1',
                    'date': '2025-07-01',
                    'processed': 1250,
                    'success': 1181,
                    'failed': 69,
                    'success_rate': '94.5%'
                },
                {
                    'key': '2',
                    'date': '2025-06-30',
                    'processed': 1500,
                    'success': 1418,
                    'failed': 82,
                    'success_rate': '94.5%'
                }
            ]
            
        elif view_type == 'province':
            columns = [
                {'title': '省份', 'dataIndex': 'province', 'key': 'province'},
                {'title': '号码数量', 'dataIndex': 'count', 'key': 'count'},
                {'title': '占比', 'dataIndex': 'percentage', 'key': 'percentage'},
                {'title': '标记数量', 'dataIndex': 'marked', 'key': 'marked'}
            ]
            
            data = [
                {
                    'key': '1',
                    'province': '广东',
                    'count': 18920,
                    'percentage': '15.1%',
                    'marked': 3784
                },
                {
                    'key': '2',
                    'province': '北京',
                    'count': 15680,
                    'percentage': '12.5%',
                    'marked': 3136
                }
            ]
            
        else:
            columns = []
            data = []
        
        return columns, data
        
    except Exception as e:
        return [], []
