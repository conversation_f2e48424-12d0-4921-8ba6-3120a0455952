"""
电话标记系统仪表板
"""
import dash
from dash import html, dcc, Input, Output, State, callback
import feffery_antd_components as fac
import feffery_antd_charts as fact
import feffery_utils_components as fuc
from datetime import datetime, timedelta
import pandas as pd

from api.phone_data import (
    get_phone_statistics_api, 
    get_device_status_api,
    get_batch_task_list_api
)


def render_phone_dashboard():
    """
    渲染电话标记系统仪表板
    """
    return html.Div([
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdTitle('电话标记识别管理系统', level=2),
                fac.AntdText('实时监控系统运行状态和数据处理情况', type='secondary')
            ], span=24)
        ], style={'marginBottom': 24}),
        
        # 统计卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总号码数量',
                        value=0,
                        valueStyle={'color': '#1890ff'},
                        prefix=fac.AntdIcon(icon='antd-phone'),
                        id='total-phone-count'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='今日处理',
                        value=0,
                        valueStyle={'color': '#52c41a'},
                        prefix=fac.AntdIcon(icon='antd-check-circle'),
                        id='today-processed-count'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='在线设备',
                        value=0,
                        valueStyle={'color': '#faad14'},
                        prefix=fac.AntdIcon(icon='antd-mobile'),
                        id='online-device-count'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='运行任务',
                        value=0,
                        valueStyle={'color': '#722ed1'},
                        prefix=fac.AntdIcon(icon='antd-clock-circle'),
                        id='running-task-count'
                    )
                ], hoverable=True)
            ], span=6)
        ], gutter=16, style={'marginBottom': 24}),
        
        # 图表区域
        fac.AntdRow([
            # 号码标记趋势图
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='号码标记趋势'),
                    html.Div(id='phone-trend-chart', style={'height': 300})
                ], title='数据趋势分析')
            ], span=12),
            
            # 标记类型分布
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='标记类型分布'),
                    html.Div(id='mark-type-chart', style={'height': 300})
                ], title='标记分类统计')
            ], span=12)
        ], gutter=16, style={'marginBottom': 24}),
        
        # 实时任务监控
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='实时任务监控'),
                    fac.AntdTable(
                        id='task-monitor-table',
                        columns=[
                            {'title': '任务名称', 'dataIndex': 'task_name', 'key': 'task_name'},
                            {'title': '任务类型', 'dataIndex': 'task_type', 'key': 'task_type'},
                            {'title': '进度', 'dataIndex': 'progress', 'key': 'progress'},
                            {'title': '状态', 'dataIndex': 'status', 'key': 'status'},
                            {'title': '开始时间', 'dataIndex': 'start_time', 'key': 'start_time'},
                        ],
                        data=[],
                        pagination={'pageSize': 5},
                        size='small'
                    )
                ], title='任务执行状态')
            ], span=24)
        ], style={'marginBottom': 24}),
        
        # 设备状态监控
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='设备连接状态'),
                    html.Div(id='device-status-chart', style={'height': 200})
                ], title='设备管理')
            ], span=12),
            
            # 系统性能监控
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='系统性能指标'),
                    html.Div([
                        fac.AntdProgress(
                            percent=0,
                            status='active',
                            strokeColor='#1890ff',
                            format={'content': 'CPU: 0%'},
                            id='cpu-usage'
                        ),
                        fac.AntdProgress(
                            percent=0,
                            status='active',
                            strokeColor='#52c41a',
                            format={'content': '内存: 0%'},
                            id='memory-usage',
                            style={'marginTop': 16}
                        ),
                        fac.AntdProgress(
                            percent=0,
                            status='active',
                            strokeColor='#faad14',
                            format={'content': '磁盘: 0%'},
                            id='disk-usage',
                            style={'marginTop': 16}
                        )
                    ])
                ], title='性能监控')
            ], span=12)
        ], gutter=16),
        
        # 自动刷新定时器
        dcc.Interval(
            id='dashboard-interval',
            interval=30*1000,  # 30秒刷新一次
            n_intervals=0
        )
    ])


# 仪表板数据更新回调
@callback(
    [Output('total-phone-count', 'value'),
     Output('today-processed-count', 'value'),
     Output('online-device-count', 'value'),
     Output('running-task-count', 'value'),
     Output('phone-trend-chart', 'children'),
     Output('mark-type-chart', 'children'),
     Output('task-monitor-table', 'data'),
     Output('device-status-chart', 'children'),
     Output('cpu-usage', 'percent'),
     Output('memory-usage', 'percent'),
     Output('disk-usage', 'percent')],
    [Input('dashboard-interval', 'n_intervals')]
)
def update_dashboard_data(n_intervals):
    """
    更新仪表板数据
    """
    try:
        # 获取统计数据
        stats_response = get_phone_statistics_api()
        device_response = get_device_status_api()
        task_response = get_batch_task_list_api({'page': 1, 'page_size': 5})
        
        # 模拟数据（实际应该从API获取）
        total_phones = 12580
        today_processed = 1250
        online_devices = 3
        running_tasks = 2
        
        # 趋势图数据
        trend_data = [
            {'date': '2025-06-25', 'count': 1200},
            {'date': '2025-06-26', 'count': 1350},
            {'date': '2025-06-27', 'count': 1180},
            {'date': '2025-06-28', 'count': 1420},
            {'date': '2025-06-29', 'count': 1380},
            {'date': '2025-06-30', 'count': 1500},
            {'date': '2025-07-01', 'count': 1250}
        ]
        
        trend_chart = fact.AntdLine(
            data=trend_data,
            xField='date',
            yField='count',
            smooth=True,
            color='#1890ff',
            point={'size': 5, 'shape': 'diamond'}
        )
        
        # 标记类型分布
        mark_data = [
            {'type': '正常号码', 'count': 8500},
            {'type': '骚扰电话', 'count': 2800},
            {'type': '诈骗电话', 'count': 980},
            {'type': '推销电话', 'count': 300}
        ]
        
        mark_chart = fact.AntdPie(
            data=mark_data,
            angleField='count',
            colorField='type',
            radius=0.8,
            label={'type': 'outer', 'content': '{name}: {percentage}'}
        )
        
        # 任务监控数据
        task_data = [
            {
                'key': '1',
                'task_name': '批量导入-20250701',
                'task_type': '导入',
                'progress': '85%',
                'status': '运行中',
                'start_time': '2025-07-01 10:30:00'
            },
            {
                'key': '2',
                'task_name': '数据处理-batch001',
                'task_type': '处理',
                'progress': '45%',
                'status': '运行中',
                'start_time': '2025-07-01 11:15:00'
            }
        ]
        
        # 设备状态图
        device_data = [
            {'status': '在线', 'count': 3},
            {'status': '离线', 'count': 1},
            {'status': '错误', 'count': 0}
        ]
        
        device_chart = fact.AntdColumn(
            data=device_data,
            xField='status',
            yField='count',
            color=['#52c41a', '#faad14', '#ff4d4f']
        )
        
        # 系统性能数据（模拟）
        cpu_usage = 45
        memory_usage = 68
        disk_usage = 32
        
        return (
            total_phones,
            today_processed,
            online_devices,
            running_tasks,
            trend_chart,
            mark_chart,
            task_data,
            device_chart,
            cpu_usage,
            memory_usage,
            disk_usage
        )
        
    except Exception as e:
        # 错误处理，返回默认值
        return (0, 0, 0, 0, html.Div(), html.Div(), [], html.Div(), 0, 0, 0)
