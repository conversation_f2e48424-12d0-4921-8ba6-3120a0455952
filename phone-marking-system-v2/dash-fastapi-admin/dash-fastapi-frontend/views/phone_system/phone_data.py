"""
电话号码数据管理页面
"""
import dash
from dash import dcc, html, Input, Output, State, callback, ctx, ALL
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, date
import pandas as pd
import json

from api.phone_data import *
from utils.common_util import validate_data_not_empty


def render_phone_data():
    """
    渲染电话号码数据管理页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '号码数据'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 查询表单
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdForm([
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdInput(
                                        id='phone-data-phone-number-input',
                                        placeholder='请输入电话号码',
                                        allowClear=True
                                    )
                                ], label='电话号码')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='phone-data-province-select',
                                        placeholder='请选择省份',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '北京', 'value': '北京'},
                                            {'label': '上海', 'value': '上海'},
                                            {'label': '广东', 'value': '广东'},
                                            {'label': '浙江', 'value': '浙江'},
                                            {'label': '江苏', 'value': '江苏'},
                                            {'label': '山东', 'value': '山东'},
                                            {'label': '四川', 'value': '四川'},
                                            {'label': '湖北', 'value': '湖北'}
                                        ],
                                        value='',
                                        allowClear=True
                                    )
                                ], label='省份')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='phone-data-status-select',
                                        placeholder='请选择状态',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '待处理', 'value': 'pending'},
                                            {'label': '处理中', 'value': 'processing'},
                                            {'label': '已完成', 'value': 'completed'},
                                            {'label': '失败', 'value': 'failed'}
                                        ],
                                        value='',
                                        allowClear=True
                                    )
                                ], label='状态')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSpace([
                                        fac.AntdButton(
                                            '查询',
                                            id='phone-data-search-btn',
                                            type='primary',
                                            icon=fac.AntdIcon(icon='antd-search')
                                        ),
                                        fac.AntdButton(
                                            '重置',
                                            id='phone-data-reset-btn',
                                            icon=fac.AntdIcon(icon='antd-sync')
                                        )
                                    ])
                                ])
                            ], span=6)
                        ], gutter=16)
                    ], layout='inline')
                ], size='small')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 操作按钮区域
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '新增',
                        id='phone-data-add-btn',
                        type='primary',
                        icon=fac.AntdIcon(icon='antd-plus')
                    ),
                    fac.AntdButton(
                        '批量删除',
                        id='phone-data-batch-delete-btn',
                        danger=True,
                        icon=fac.AntdIcon(icon='antd-delete')
                    ),
                    fac.AntdButton(
                        '导出',
                        id='phone-data-export-btn',
                        icon=fac.AntdIcon(icon='antd-download')
                    ),
                    fac.AntdButton(
                        '刷新',
                        id='phone-data-refresh-btn',
                        icon=fac.AntdIcon(icon='antd-reload')
                    )
                ])
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 数据表格
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTable(
                        id='phone-data-table',
                        columns=[
                            {
                                'title': '选择',
                                'dataIndex': 'selection',
                                'key': 'selection',
                                'width': 50,
                                'renderOptions': {'renderType': 'checkbox'}
                            },
                            {'title': '电话号码', 'dataIndex': 'phone_number', 'key': 'phone_number', 'width': 120},
                            {'title': '号码类型', 'dataIndex': 'phone_type', 'key': 'phone_type', 'width': 80},
                            {'title': '省份', 'dataIndex': 'province', 'key': 'province', 'width': 80},
                            {'title': '城市', 'dataIndex': 'city', 'key': 'city', 'width': 80},
                            {'title': '运营商', 'dataIndex': 'operator', 'key': 'operator', 'width': 80},
                            {'title': '标记数量', 'dataIndex': 'mark_count', 'key': 'mark_count', 'width': 80},
                            {'title': '标记类型', 'dataIndex': 'mark_type', 'key': 'mark_type', 'width': 100},
                            {'title': '置信度', 'dataIndex': 'confidence_score', 'key': 'confidence_score', 'width': 80},
                            {'title': '状态', 'dataIndex': 'status', 'key': 'status', 'width': 80},
                            {'title': '创建时间', 'dataIndex': 'created_at', 'key': 'created_at', 'width': 150},
                            {'title': '操作', 'dataIndex': 'operation', 'key': 'operation', 'width': 150}
                        ],
                        data=[],
                        pagination={
                            'pageSize': 20,
                            'current': 1,
                            'showSizeChanger': True,
                            'showQuickJumper': True,
                            'showTotal': True
                        },
                        rowSelection={
                            'type': 'checkbox',
                            'selectedRowKeys': []
                        },
                        bordered=True,
                        size='small'
                    )
                ])
            ], span=24)
        ]),
        
        # 新增/编辑模态框
        fac.AntdModal(
            id='phone-data-modal',
            title='电话号码信息',
            visible=False,
            width=600,
            children=[
                fac.AntdForm([
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='phone-data-modal-phone-number',
                            placeholder='请输入电话号码'
                        )
                    ], label='电话号码', required=True),
                    
                    fac.AntdFormItem([
                        fac.AntdSelect(
                            id='phone-data-modal-phone-type',
                            options=[
                                {'label': '手机号码', 'value': 'mobile'},
                                {'label': '固定电话', 'value': 'landline'}
                            ],
                            placeholder='请选择号码类型'
                        )
                    ], label='号码类型'),
                    
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='phone-data-modal-province',
                            placeholder='请输入省份'
                        )
                    ], label='省份'),
                    
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='phone-data-modal-city',
                            placeholder='请输入城市'
                        )
                    ], label='城市'),
                    
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='phone-data-modal-operator',
                            placeholder='请输入运营商'
                        )
                    ], label='运营商'),
                    
                    fac.AntdFormItem([
                        fac.AntdInputNumber(
                            id='phone-data-modal-mark-count',
                            min=0,
                            placeholder='请输入标记数量'
                        )
                    ], label='标记数量'),
                    
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='phone-data-modal-mark-type',
                            placeholder='请输入标记类型'
                        )
                    ], label='标记类型')
                ], layout='vertical')
            ],
            okText='确定',
            cancelText='取消'
        ),
        
        # 消息提示容器
        html.Div(id='phone-data-message-container'),
        
        # 存储组件
        dcc.Store(id='phone-data-current-page', data=1),
        dcc.Store(id='phone-data-page-size', data=20),
        dcc.Store(id='phone-data-edit-id', data=None)
    ]


# 查询数据回调
@callback(
    [Output('phone-data-table', 'data'),
     Output('phone-data-table', 'pagination'),
     Output('phone-data-message-container', 'children')],
    [Input('phone-data-search-btn', 'n_clicks'),
     Input('phone-data-refresh-btn', 'n_clicks'),
     Input('phone-data-table', 'pagination')],
    [State('phone-data-phone-number-input', 'value'),
     State('phone-data-province-select', 'value'),
     State('phone-data-status-select', 'value')],
    prevent_initial_call=False
)
def search_phone_data(search_clicks, refresh_clicks, pagination, phone_number, province, status):
    """
    查询电话号码数据
    """
    try:
        # 构建查询参数
        query_params = {}
        if phone_number:
            query_params['phone_number'] = phone_number
        if province:
            query_params['province'] = province
        if status:
            query_params['status'] = status
        
        # 分页参数
        page = pagination.get('current', 1) if pagination else 1
        page_size = pagination.get('pageSize', 20) if pagination else 20
        
        # 模拟API调用（实际应该调用真实API）
        # response = get_phone_data_list_api({'page': page, 'page_size': page_size}, query_params)
        
        # 模拟数据
        mock_data = []
        for i in range(1, 21):
            mock_data.append({
                'key': str(i),
                'phone_number': f'1380013800{i:02d}',
                'phone_type': '手机号码',
                'province': '北京' if i % 2 == 0 else '上海',
                'city': '北京' if i % 2 == 0 else '上海',
                'operator': '中国移动',
                'mark_count': i * 2,
                'mark_type': '正常号码' if i % 3 == 0 else '骚扰电话',
                'confidence_score': f'{85 + i}%',
                'status': fac.AntdTag(
                    content='已完成',
                    color='success'
                ),
                'created_at': '2025-07-01 12:00:00',
                'operation': fac.AntdSpace([
                    fac.AntdButton(
                        '编辑',
                        id={'type': 'phone-data-edit-btn', 'index': i},
                        type='link',
                        size='small'
                    ),
                    fac.AntdButton(
                        '删除',
                        id={'type': 'phone-data-delete-btn', 'index': i},
                        type='link',
                        size='small',
                        danger=True
                    )
                ])
            })
        
        # 更新分页信息
        new_pagination = {
            'current': page,
            'pageSize': page_size,
            'total': 100,  # 模拟总数
            'showSizeChanger': True,
            'showQuickJumper': True,
            'showTotal': True
        }
        
        return mock_data, new_pagination, None
        
    except Exception as e:
        return [], {}, fac.AntdMessage(content=f'查询失败: {str(e)}', type='error')


# 重置查询条件回调
@callback(
    [Output('phone-data-phone-number-input', 'value'),
     Output('phone-data-province-select', 'value'),
     Output('phone-data-status-select', 'value')],
    [Input('phone-data-reset-btn', 'n_clicks')],
    prevent_initial_call=True
)
def reset_search_form(n_clicks):
    """
    重置查询表单
    """
    if n_clicks:
        return '', '', ''
    return dash.no_update, dash.no_update, dash.no_update
