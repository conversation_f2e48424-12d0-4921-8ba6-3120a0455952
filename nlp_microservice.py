"""
NLP分析微服务
提供智能文本分析和分类的RESTful API服务
采用保守的微服务化策略，保持向后兼容性

设计原则：
1. 单一职责：专注于文本分析和分类功能
2. 无状态设计：每个请求独立处理
3. 向后兼容：保持与原有SmartTextAnalyzer的接口兼容
4. 容错设计：优雅处理各种异常情况
5. 性能优化：内置缓存和批处理支持
"""

import asyncio
import logging
import time
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from contextlib import asynccontextmanager

# 尝试导入FastAPI，如果不存在则提供降级方案
try:
    from fastapi import FastAPI, HTTPException, Query, Path
    from fastapi.responses import JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel, Field
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI不可用，将使用基础HTTP服务器")

# 导入现有的智能文本分析器
try:
    from smart_text_analyzer import SmartTextAnalyzer
    ANALYZER_AVAILABLE = True
except ImportError:
    ANALYZER_AVAILABLE = False
    logging.warning("SmartTextAnalyzer不可用，将使用模拟分析")


class TextAnalysisRequest(BaseModel):
    """文本分析请求模型"""
    text: str = Field(..., description="待分析的文本", example="这是一个推销电话")
    phone_number: Optional[str] = Field(None, description="关联的电话号码", example="13800138000")
    include_details: bool = Field(True, description="是否包含详细分析信息")
    
    
class BatchAnalysisRequest(BaseModel):
    """批量文本分析请求模型"""
    texts: List[str] = Field(..., description="待分析的文本列表")
    phone_numbers: Optional[List[str]] = Field(None, description="关联的电话号码列表")
    include_details: bool = Field(True, description="是否包含详细分析信息")


class TextAnalysisResponse(BaseModel):
    """文本分析响应模型"""
    status: str = Field(..., description="响应状态", example="success")
    data: Optional[Dict[str, Any]] = Field(None, description="分析结果")
    message: Optional[str] = Field(None, description="错误消息")
    processing_time_ms: float = Field(..., description="处理时间（毫秒）")
    cache_hit: bool = Field(False, description="是否命中缓存")


class BatchAnalysisResponse(BaseModel):
    """批量分析响应模型"""
    status: str = Field(..., description="响应状态", example="success")
    data: Optional[Dict[str, Any]] = Field(None, description="批量分析结果")
    processing_time_ms: float = Field(..., description="总处理时间（毫秒）")
    total_count: int = Field(..., description="总文本数量")
    success_count: int = Field(..., description="成功分析数量")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态", example="healthy")
    service: str = Field(..., description="服务名称", example="nlp")
    version: str = Field(..., description="服务版本", example="1.0.0")
    uptime_seconds: float = Field(..., description="运行时间（秒）")
    total_requests: int = Field(..., description="总请求数")
    analysis_stats: Dict[str, Any] = Field(..., description="分析统计")


class NLPMicroservice:
    """
    NLP分析微服务
    
    功能特性：
    - RESTful API接口
    - 异步处理支持
    - 批量分析能力
    - 内置缓存机制
    - 性能监控
    - 健康检查
    - 向后兼容接口
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化NLP微服务
        
        Args:
            config: 服务配置字典
        """
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)
        
        # 服务状态
        self.start_time = time.time()
        self.total_requests = 0
        self.successful_analyses = 0
        self.failed_analyses = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 分类统计
        self.category_stats = {
            'spam': 0,
            'fraud': 0,
            'business': 0,
            'personal': 0,
            'unknown': 0
        }
        
        # 初始化文本分析器
        self.text_analyzer = self._init_text_analyzer()
        
        # 初始化FastAPI应用（如果可用）
        if FASTAPI_AVAILABLE:
            self.app = self._create_fastapi_app()
        else:
            self.app = None
            self.logger.warning("FastAPI不可用，微服务功能受限")
        
        self.logger.info("NLP分析微服务初始化完成")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'host': '127.0.0.1',
            'port': 8002,
            'debug': False,
            'enable_cors': True,
            'cache_enabled': True,
            'cache_ttl': 1800,  # 缓存30分钟
            'max_cache_size': 5000,
            'request_timeout': 30.0,
            'log_level': 'INFO',
            'max_batch_size': 50,  # 最大批处理大小
            'enable_deep_analysis': False  # 默认关闭深度分析
        }
    
    def _init_text_analyzer(self) -> Optional[SmartTextAnalyzer]:
        """
        初始化文本分析器
        
        Returns:
            SmartTextAnalyzer实例或None（如果不可用）
        """
        if ANALYZER_AVAILABLE:
            try:
                # 使用配置初始化分析器
                analyzer_config = {
                    'enable_cache': self.config.get('cache_enabled', True),
                    'cache_ttl': self.config.get('cache_ttl', 1800),
                    'enable_deep_analysis': self.config.get('enable_deep_analysis', False),
                    'max_processing_time': 0.1  # 100ms限制
                }
                
                analyzer = SmartTextAnalyzer('nlp_microservice_config.json')
                # 更新配置
                analyzer.config.update(analyzer_config)
                
                self.logger.info("SmartTextAnalyzer初始化成功")
                return analyzer
            except Exception as e:
                self.logger.error(f"SmartTextAnalyzer初始化失败: {e}")
                return None
        else:
            self.logger.warning("SmartTextAnalyzer不可用，将使用模拟分析")
            return None
    
    def _create_fastapi_app(self) -> FastAPI:
        """
        创建FastAPI应用
        
        Returns:
            配置好的FastAPI应用实例
        """
        # 创建应用实例
        app = FastAPI(
            title="NLP分析微服务",
            description="提供智能文本分析和分类的RESTful API服务",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 配置CORS
        if self.config.get('enable_cors', True):
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
        
        # 注册路由
        self._register_routes(app)
        
        return app
    
    def _register_routes(self, app: FastAPI):
        """
        注册API路由
        
        Args:
            app: FastAPI应用实例
        """
        
        @app.get("/health", response_model=HealthResponse)
        async def health_check():
            """健康检查接口"""
            uptime = time.time() - self.start_time
            
            analysis_stats = {
                'total_requests': self.total_requests,
                'successful_analyses': self.successful_analyses,
                'failed_analyses': self.failed_analyses,
                'success_rate': (self.successful_analyses / max(self.total_requests, 1)) * 100,
                'cache_hits': self.cache_hits,
                'cache_misses': self.cache_misses,
                'cache_hit_rate': (self.cache_hits / max(self.total_requests, 1)) * 100,
                'category_distribution': self.category_stats
            }
            
            return HealthResponse(
                status="healthy",
                service="nlp",
                version="1.0.0",
                uptime_seconds=uptime,
                total_requests=self.total_requests,
                analysis_stats=analysis_stats
            )
        
        @app.post("/api/v1/analyze", response_model=TextAnalysisResponse)
        async def analyze_text(request: TextAnalysisRequest):
            """
            分析单个文本
            
            Args:
                request: 文本分析请求
                
            Returns:
                文本分析结果
            """
            return await self._process_text_analysis(
                request.text, 
                request.phone_number,
                request.include_details
            )
        
        @app.get("/api/v1/analyze/{text}", response_model=TextAnalysisResponse)
        async def analyze_text_by_path(
            text: str = Path(..., description="待分析的文本", example="推销电话"),
            phone_number: Optional[str] = Query(None, description="关联的电话号码"),
            include_details: bool = Query(True, description="是否包含详细信息")
        ):
            """
            通过路径参数分析文本
            
            Args:
                text: 待分析的文本
                phone_number: 关联的电话号码
                include_details: 是否包含详细信息
                
            Returns:
                文本分析结果
            """
            return await self._process_text_analysis(text, phone_number, include_details)
        
        @app.post("/api/v1/batch-analyze", response_model=BatchAnalysisResponse)
        async def batch_analyze_texts(request: BatchAnalysisRequest):
            """
            批量分析文本
            
            Args:
                request: 批量分析请求
                
            Returns:
                批量分析结果
            """
            if len(request.texts) > self.config.get('max_batch_size', 50):
                raise HTTPException(
                    status_code=400, 
                    detail=f"批量分析数量不能超过{self.config.get('max_batch_size', 50)}个"
                )
            
            return await self._process_batch_analysis(
                request.texts,
                request.phone_numbers,
                request.include_details
            )
        
        @app.get("/api/v1/categories")
        async def get_supported_categories():
            """获取支持的分类类别"""
            categories = {
                'spam': {
                    'name': '垃圾信息',
                    'description': '推销、广告、营销等垃圾信息',
                    'keywords': ['推销', '广告', '营销', '促销', '优惠']
                },
                'fraud': {
                    'name': '诈骗信息',
                    'description': '诈骗、欺诈、虚假信息',
                    'keywords': ['诈骗', '中奖', '验证码', '转账', '汇款']
                },
                'business': {
                    'name': '商业信息',
                    'description': '正常的商业服务信息',
                    'keywords': ['快递', '外卖', '客服', '通知', '预约']
                },
                'personal': {
                    'name': '个人信息',
                    'description': '个人联系人信息',
                    'keywords': ['朋友', '家人', '同事', '同学', '熟人']
                },
                'unknown': {
                    'name': '未知信息',
                    'description': '无法确定类别的信息',
                    'keywords': ['未知', '陌生', '不明']
                }
            }
            
            return {
                'status': 'success',
                'data': {
                    'categories': categories,
                    'total_count': len(categories)
                }
            }
        
        @app.get("/api/v1/stats")
        async def get_service_stats():
            """获取服务统计信息"""
            uptime = time.time() - self.start_time
            
            stats = {
                'service_info': {
                    'name': 'nlp-microservice',
                    'version': '1.0.0',
                    'uptime_seconds': uptime,
                    'uptime_formatted': self._format_uptime(uptime)
                },
                'request_stats': {
                    'total_requests': self.total_requests,
                    'successful_analyses': self.successful_analyses,
                    'failed_analyses': self.failed_analyses,
                    'success_rate': (self.successful_analyses / max(self.total_requests, 1)) * 100,
                    'requests_per_second': self.total_requests / max(uptime, 1)
                },
                'cache_stats': {
                    'cache_enabled': self.config.get('cache_enabled', True),
                    'cache_hits': self.cache_hits,
                    'cache_misses': self.cache_misses,
                    'cache_hit_rate': (self.cache_hits / max(self.total_requests, 1)) * 100
                },
                'analysis_stats': {
                    'category_distribution': self.category_stats,
                    'most_common_category': max(self.category_stats.items(), key=lambda x: x[1])[0] if any(self.category_stats.values()) else 'unknown'
                },
                'analyzer_status': {
                    'available': self.text_analyzer is not None,
                    'type': 'SmartTextAnalyzer' if self.text_analyzer else 'Mock'
                }
            }
            
            return {'status': 'success', 'data': stats}
    
    async def _process_text_analysis(self, text: str, phone_number: Optional[str] = None, 
                                   include_details: bool = True) -> TextAnalysisResponse:
        """
        处理文本分析请求
        
        Args:
            text: 待分析的文本
            phone_number: 关联的电话号码
            include_details: 是否包含详细信息
            
        Returns:
            分析结果
        """
        start_time = time.time()
        self.total_requests += 1
        cache_hit = False
        
        try:
            # 验证输入
            if not text or not text.strip():
                raise ValueError("文本内容不能为空")
            
            # 进行文本分析
            if self.text_analyzer:
                # 使用真实的文本分析器
                result = self.text_analyzer.analyze_text(text, phone_number or '')
                if result:
                    self.successful_analyses += 1
                    cache_hit = result.get('method') == 'cache_hit'
                    if cache_hit:
                        self.cache_hits += 1
                    else:
                        self.cache_misses += 1
                    
                    # 更新分类统计
                    category = result.get('category', 'unknown')
                    if category in self.category_stats:
                        self.category_stats[category] += 1
                else:
                    self.failed_analyses += 1
                    self.cache_misses += 1
            else:
                # 使用模拟分析
                result = self._get_mock_analysis(text, phone_number)
                self.successful_analyses += 1
                self.cache_misses += 1
            
            processing_time = (time.time() - start_time) * 1000
            
            # 构建响应数据
            response_data = {
                'category': result.get('category', 'unknown'),
                'confidence': result.get('confidence', 0.0),
                'method': result.get('method', 'unknown'),
                'timestamp': result.get('timestamp', datetime.now().isoformat())
            }
            
            if include_details:
                response_data['details'] = result.get('details', {})
            
            return TextAnalysisResponse(
                status="success",
                data=response_data,
                processing_time_ms=processing_time,
                cache_hit=cache_hit
            )
                
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            self.failed_analyses += 1
            self.cache_misses += 1
            self.logger.error(f"文本分析失败: {e}")
            
            return TextAnalysisResponse(
                status="error",
                message=str(e),
                processing_time_ms=processing_time,
                cache_hit=False
            )
    
    async def _process_batch_analysis(self, texts: List[str], 
                                    phone_numbers: Optional[List[str]] = None,
                                    include_details: bool = True) -> BatchAnalysisResponse:
        """
        处理批量文本分析请求
        
        Args:
            texts: 待分析的文本列表
            phone_numbers: 关联的电话号码列表
            include_details: 是否包含详细信息
            
        Returns:
            批量分析结果
        """
        start_time = time.time()
        
        try:
            # 准备电话号码列表
            if phone_numbers is None:
                phone_numbers = [None] * len(texts)
            elif len(phone_numbers) != len(texts):
                # 如果电话号码数量不匹配，用None补齐
                phone_numbers = phone_numbers + [None] * (len(texts) - len(phone_numbers))
            
            # 并发处理所有文本
            tasks = [
                self._process_text_analysis(text, phone, include_details)
                for text, phone in zip(texts, phone_numbers)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            successful_results = []
            failed_results = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_results.append({
                        'index': i,
                        'text': texts[i][:50] + '...' if len(texts[i]) > 50 else texts[i],
                        'error': str(result)
                    })
                elif result.status == 'success':
                    successful_results.append({
                        'index': i,
                        'text': texts[i][:50] + '...' if len(texts[i]) > 50 else texts[i],
                        'analysis': result.data
                    })
                else:
                    failed_results.append({
                        'index': i,
                        'text': texts[i][:50] + '...' if len(texts[i]) > 50 else texts[i],
                        'error': result.message
                    })
            
            processing_time = (time.time() - start_time) * 1000
            
            return BatchAnalysisResponse(
                status="success",
                data={
                    'successful_results': successful_results,
                    'failed_results': failed_results,
                    'summary': {
                        'total_texts': len(texts),
                        'successful_count': len(successful_results),
                        'failed_count': len(failed_results),
                        'success_rate': (len(successful_results) / len(texts)) * 100
                    }
                },
                processing_time_ms=processing_time,
                total_count=len(texts),
                success_count=len(successful_results)
            )
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            self.logger.error(f"批量文本分析失败: {e}")
            
            return BatchAnalysisResponse(
                status="error",
                data={'error': str(e)},
                processing_time_ms=processing_time,
                total_count=len(texts),
                success_count=0
            )
    
    def _get_mock_analysis(self, text: str, phone_number: Optional[str] = None) -> Dict[str, Any]:
        """
        获取模拟分析结果（当SmartTextAnalyzer不可用时使用）
        
        Args:
            text: 待分析的文本
            phone_number: 关联的电话号码
            
        Returns:
            模拟的分析结果
        """
        # 简单的关键词匹配逻辑
        text_lower = text.lower()
        
        if any(keyword in text_lower for keyword in ['推销', '广告', '营销', '促销', '优惠']):
            category = 'spam'
            confidence = 0.8
        elif any(keyword in text_lower for keyword in ['诈骗', '中奖', '验证码', '转账', '汇款']):
            category = 'fraud'
            confidence = 0.9
        elif any(keyword in text_lower for keyword in ['快递', '外卖', '客服', '通知', '预约']):
            category = 'business'
            confidence = 0.7
        elif any(keyword in text_lower for keyword in ['朋友', '家人', '同事', '同学', '熟人']):
            category = 'personal'
            confidence = 0.6
        else:
            category = 'unknown'
            confidence = 0.3
        
        return {
            'category': category,
            'confidence': confidence,
            'method': 'mock_analysis',
            'details': {
                'text_length': len(text),
                'phone_number': phone_number,
                'source': 'mock'
            },
            'timestamp': datetime.now().isoformat()
        }
    
    def _format_uptime(self, uptime_seconds: float) -> str:
        """
        格式化运行时间
        
        Args:
            uptime_seconds: 运行时间（秒）
            
        Returns:
            格式化的时间字符串
        """
        hours = int(uptime_seconds // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        seconds = int(uptime_seconds % 60)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def run(self, host: Optional[str] = None, port: Optional[int] = None):
        """
        运行微服务
        
        Args:
            host: 监听地址
            port: 监听端口
        """
        if not FASTAPI_AVAILABLE:
            self.logger.error("FastAPI不可用，无法启动微服务")
            return
        
        host = host or self.config.get('host', '127.0.0.1')
        port = port or self.config.get('port', 8002)
        
        self.logger.info(f"启动NLP分析微服务: http://{host}:{port}")
        self.logger.info(f"API文档: http://{host}:{port}/docs")
        
        try:
            uvicorn.run(
                self.app,
                host=host,
                port=port,
                log_level=self.config.get('log_level', 'info').lower()
            )
        except Exception as e:
            self.logger.error(f"微服务启动失败: {e}")
    
    # 向后兼容的同步接口
    def analyze_text(self, text: str, phone_number: str = '') -> Dict[str, Any]:
        """
        同步分析文本（向后兼容接口）
        
        Args:
            text: 待分析的文本
            phone_number: 关联的电话号码
            
        Returns:
            分析结果字典
        """
        if self.text_analyzer:
            return self.text_analyzer.analyze_text(text, phone_number)
        else:
            return self._get_mock_analysis(text, phone_number)


def create_nlp_microservice(config: Optional[Dict[str, Any]] = None) -> NLPMicroservice:
    """
    创建NLP分析微服务实例
    
    Args:
        config: 服务配置
        
    Returns:
        NLPMicroservice实例
    """
    return NLPMicroservice(config)


def main():
    """主函数：启动微服务"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建并启动微服务
    service = create_nlp_microservice()
    service.run()


if __name__ == '__main__':
    main()
