"""
第一阶段优化功能集成测试
测试品牌优化、数据库优化、并发处理的协同工作
"""

import sys
import os
import time
import logging
from typing import List, Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_brand_optimization():
    """测试品牌优化功能"""
    print("=== 测试品牌优化功能 ===")
    
    try:
        from brand_optimizer import BrandSpecificOptimizer
        
        optimizer = BrandSpecificOptimizer()
        
        # 测试不同品牌配置
        test_devices = [
            {'brand': 'huawei', 'model': 'MED-AL00'},
            {'brand': 'xiaomi', 'model': 'MI 10'},
            {'brand': 'samsung', 'model': 'Galaxy S21'},
            {'brand': 'unknown', 'model': 'Unknown Device'}
        ]
        
        for device in test_devices:
            config = optimizer.get_brand_config(device)
            methods = optimizer.get_preferred_methods(device)
            
            print(f"✓ 品牌: {device['brand']}")
            print(f"  推荐方法: {methods}")
            print(f"  亮度调整: {config['brightness_adjust']}")
            print(f"  对比度增强: {config['contrast_enhance']}")
            
            # 模拟性能记录
            optimizer.record_performance(device, True, 0.9)
        
        # 获取性能报告
        report = optimizer.get_performance_report()
        print(f"✓ 性能报告生成成功，覆盖品牌数: {report['total_brands']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 品牌优化测试失败: {e}")
        return False

def test_database_optimization():
    """测试数据库优化功能"""
    print("\n=== 测试数据库优化功能 ===")
    
    try:
        from optimized_db_manager import OptimizedDatabaseManager
        
        db = OptimizedDatabaseManager()
        
        # 测试批量保存
        test_records = []
        for i in range(5):
            record = {
                'phone_number': f'1380013800{i}',
                'mark_info': f'测试标记{i}',
                'province': '北京',
                'city': '北京',
                'isp': '中国移动',
                'location_source': 'database',
                'phone_model': 'Test Phone',
                'android_version': '10',
                'status': 'success',
                'processing_time': 2.0 + i * 0.1,
                'method': 'ML Kit',
                'retry_count': 0
            }
            test_records.append(record)
        
        # 批量保存测试
        start_time = time.time()
        success_count = 0
        
        for record in test_records:
            if db.save_dial_record(record):
                success_count += 1
        
        save_time = time.time() - start_time
        
        print(f"✓ 批量保存测试: {success_count}/{len(test_records)} 成功")
        print(f"✓ 保存耗时: {save_time:.3f}秒")
        
        # 测试查询性能
        start_time = time.time()
        records = db.get_recent_records(10)
        query_time = time.time() - start_time
        
        print(f"✓ 查询测试: 获取到 {len(records)} 条记录")
        print(f"✓ 查询耗时: {query_time:.3f}秒")
        
        # 测试缓存
        start_time = time.time()
        cached_records = db.get_recent_records(10)  # 应该命中缓存
        cache_time = time.time() - start_time
        
        print(f"✓ 缓存测试: 耗时 {cache_time:.3f}秒")
        
        # 获取性能统计
        stats = db.get_performance_stats()
        print(f"✓ 性能统计: 平均查询时间 {stats['avg_query_time']}ms, "
              f"缓存命中率 {stats['cache_hit_rate']}%")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库优化测试失败: {e}")
        return False

def test_concurrent_processing():
    """测试并发处理功能"""
    print("\n=== 测试并发处理功能 ===")
    
    try:
        from concurrent_processor import SafeConcurrentProcessor
        
        def mock_phone_processor(phone_number, device):
            """模拟电话处理函数"""
            # 模拟处理时间
            time.sleep(0.2)
            
            return {
                'phone_number': phone_number,
                'device_id': device['id'],
                'mark_info': f'标记信息_{phone_number}',
                'status': 'success',
                'processing_time': 0.2
            }
        
        processor = SafeConcurrentProcessor(max_workers=2)
        
        # 测试号码列表
        test_phones = [f'1380013800{i}' for i in range(8)]
        
        print(f"✓ 开始并发处理 {len(test_phones)} 个号码...")
        
        start_time = time.time()
        results = processor.process_batch_safe(test_phones, mock_phone_processor)
        end_time = time.time()
        
        # 统计结果
        successful = sum(1 for r in results if r.get('status') == 'success')
        failed = len(results) - successful
        
        print(f"✓ 并发处理完成: {successful} 成功, {failed} 失败")
        print(f"✓ 总耗时: {end_time - start_time:.2f}秒")
        print(f"✓ 平均每个号码: {(end_time - start_time) / len(test_phones):.2f}秒")
        
        # 获取统计信息
        stats = processor.get_stats()
        print(f"✓ 处理统计: 成功率 {stats['success_rate']}%, 最大并发数 {stats['max_workers']}")
        
        processor.shutdown()
        return True
        
    except Exception as e:
        print(f"✗ 并发处理测试失败: {e}")
        return False

def test_integration():
    """集成测试 - 测试所有优化功能的协同工作"""
    print("\n=== 集成测试 ===")
    
    try:
        from brand_optimizer import BrandSpecificOptimizer
        from optimized_db_manager import OptimizedDatabaseManager
        from concurrent_processor import SafeConcurrentProcessor
        
        # 初始化所有组件
        brand_optimizer = BrandSpecificOptimizer()
        db_manager = OptimizedDatabaseManager()
        concurrent_processor = SafeConcurrentProcessor(max_workers=2)
        
        def integrated_processor(phone_number, device):
            """集成处理函数"""
            # 模拟设备信息
            device_info = {'brand': 'huawei', 'model': 'MED-AL00'}
            
            # 获取品牌配置
            config = brand_optimizer.get_brand_config(device_info)
            methods = brand_optimizer.get_preferred_methods(device_info)
            
            # 模拟处理
            time.sleep(0.1)
            
            # 创建记录
            record = {
                'phone_number': phone_number,
                'mark_info': f'集成测试标记_{phone_number}',
                'province': '北京',
                'city': '北京',
                'isp': '中国移动',
                'location_source': 'database',
                'phone_model': f"{device_info['brand']} {device_info['model']}",
                'android_version': '10',
                'status': 'success',
                'processing_time': 0.1,
                'method': 'ML Kit Optimized',
                'retry_count': 0
            }
            
            # 保存到数据库
            db_success = db_manager.save_dial_record(record)
            
            # 记录品牌性能
            brand_optimizer.record_performance(device_info, db_success, 0.9)
            
            return {
                'phone_number': phone_number,
                'status': 'success' if db_success else 'failed',
                'brand_config': config,
                'preferred_methods': methods,
                'db_saved': db_success
            }
        
        # 执行集成测试
        test_phones = [f'1390013900{i}' for i in range(6)]
        
        print(f"✓ 开始集成测试，处理 {len(test_phones)} 个号码...")
        
        start_time = time.time()
        results = concurrent_processor.process_batch_safe(test_phones, integrated_processor)
        end_time = time.time()
        
        # 统计结果
        successful = sum(1 for r in results if r.get('status') == 'success')
        db_saved = sum(1 for r in results if r.get('db_saved', False))
        
        print(f"✓ 集成测试完成:")
        print(f"  - 处理成功: {successful}/{len(test_phones)}")
        print(f"  - 数据库保存: {db_saved}/{len(test_phones)}")
        print(f"  - 总耗时: {end_time - start_time:.2f}秒")
        
        # 获取各组件统计
        brand_report = brand_optimizer.get_performance_report()
        db_stats = db_manager.get_performance_stats()
        concurrent_stats = concurrent_processor.get_stats()
        
        print(f"✓ 品牌优化: 覆盖 {brand_report['total_brands']} 个品牌")
        print(f"✓ 数据库性能: 平均查询 {db_stats['avg_query_time']}ms")
        print(f"✓ 并发处理: 成功率 {concurrent_stats['success_rate']}%")
        
        # 清理
        concurrent_processor.shutdown()
        db_manager.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始第一阶段优化功能测试...\n")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_brand_optimization())
    test_results.append(test_database_optimization())
    test_results.append(test_concurrent_processing())
    test_results.append(test_integration())
    
    # 总结测试结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"\n=== 测试结果总结 ===")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 第一阶段优化功能全部测试通过！")
        print("\n✅ 已实现的优化:")
        print("  1. 品牌特定的图像预处理优化")
        print("  2. SQLite数据库性能优化")
        print("  3. 安全的并发处理能力")
        print("  4. 各组件的无缝集成")
        
        print("\n📈 性能提升预期:")
        print("  - 图像识别准确率提升: 5-10%")
        print("  - 数据库查询速度提升: 2-3倍")
        print("  - 批量处理速度提升: 2-4倍")
        print("  - 系统稳定性显著增强")
        
        print("\n🔄 下一步计划:")
        print("  - 实施ML Kit智能预处理选择")
        print("  - 集成NLP技术进行语义理解")
        print("  - 实现智能调度优化")
        
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        failed_tests = total_tests - passed_tests
        print(f"失败测试数: {failed_tests}")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
