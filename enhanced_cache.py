"""
增强缓存系统
提供多层缓存、智能过期、缓存预热等高级缓存功能
采用保守的实施策略，支持多种缓存后端

设计原则：
1. 多层缓存：内存缓存 + 可选的Redis缓存
2. 智能过期：LRU、TTL、访问频率等多种策略
3. 缓存预热：支持预加载热点数据
4. 向后兼容：可以替换现有的简单缓存
5. 性能监控：详细的缓存统计和命中率分析
"""

import time
import json
import threading
import hashlib
import logging
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from collections import OrderedDict, defaultdict
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
import pickle

# 尝试导入Redis，如果不可用则提供降级方案
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis不可用，将使用内存缓存")


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_time: float
    last_access_time: float
    access_count: int
    ttl: Optional[float] = None
    size_bytes: int = 0
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_time > self.ttl
    
    def update_access(self):
        """更新访问信息"""
        self.last_access_time = time.time()
        self.access_count += 1


@dataclass
class CacheStats:
    """缓存统计"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    evictions: int = 0
    expired_entries: int = 0
    total_size_bytes: int = 0
    avg_access_time_ms: float = 0.0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        if self.total_requests == 0:
            return 0.0
        return (self.cache_hits / self.total_requests) * 100
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 100.0 - self.hit_rate


class CacheBackend(ABC):
    """缓存后端抽象基类"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """清空缓存"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        pass


class MemoryCacheBackend(CacheBackend):
    """内存缓存后端"""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = None):
        """
        初始化内存缓存
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认TTL（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.stats = CacheStats()
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        start_time = time.time()
        
        with self._lock:
            self.stats.total_requests += 1
            
            if key not in self.cache:
                self.stats.cache_misses += 1
                self._update_avg_access_time(start_time)
                return None
            
            entry = self.cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self.cache[key]
                self.stats.cache_misses += 1
                self.stats.expired_entries += 1
                self._update_avg_access_time(start_time)
                return None
            
            # 更新访问信息
            entry.update_access()
            
            # 移动到末尾（LRU）
            self.cache.move_to_end(key)
            
            self.stats.cache_hits += 1
            self._update_avg_access_time(start_time)
            
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """设置缓存值"""
        try:
            with self._lock:
                # 计算值的大小
                size_bytes = self._calculate_size(value)
                
                # 使用指定的TTL或默认TTL
                effective_ttl = ttl if ttl is not None else self.default_ttl
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    created_time=time.time(),
                    last_access_time=time.time(),
                    access_count=0,
                    ttl=effective_ttl,
                    size_bytes=size_bytes
                )
                
                # 如果键已存在，更新统计
                if key in self.cache:
                    old_entry = self.cache[key]
                    self.stats.total_size_bytes -= old_entry.size_bytes
                
                # 检查是否需要驱逐
                while len(self.cache) >= self.max_size and key not in self.cache:
                    self._evict_lru()
                
                # 添加到缓存
                self.cache[key] = entry
                self.stats.total_size_bytes += size_bytes
                
                return True
                
        except Exception as e:
            self.logger.error(f"设置缓存失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        with self._lock:
            if key in self.cache:
                entry = self.cache.pop(key)
                self.stats.total_size_bytes -= entry.size_bytes
                return True
            return False
    
    def clear(self) -> bool:
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.stats.total_size_bytes = 0
            return True
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        with self._lock:
            if key not in self.cache:
                return False
            
            entry = self.cache[key]
            if entry.is_expired():
                del self.cache[key]
                self.stats.expired_entries += 1
                return False
            
            return True
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                'backend_type': 'memory',
                'cache_size': len(self.cache),
                'max_size': self.max_size,
                'total_size_bytes': self.stats.total_size_bytes,
                'total_requests': self.stats.total_requests,
                'cache_hits': self.stats.cache_hits,
                'cache_misses': self.stats.cache_misses,
                'hit_rate': self.stats.hit_rate,
                'evictions': self.stats.evictions,
                'expired_entries': self.stats.expired_entries,
                'avg_access_time_ms': self.stats.avg_access_time_ms
            }
    
    def _evict_lru(self):
        """驱逐最近最少使用的条目"""
        if self.cache:
            key, entry = self.cache.popitem(last=False)
            self.stats.total_size_bytes -= entry.size_bytes
            self.stats.evictions += 1
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小"""
        try:
            return len(pickle.dumps(value))
        except:
            return len(str(value).encode('utf-8'))
    
    def _update_avg_access_time(self, start_time: float):
        """更新平均访问时间"""
        access_time = (time.time() - start_time) * 1000  # 转换为毫秒
        if self.stats.total_requests == 1:
            self.stats.avg_access_time_ms = access_time
        else:
            # 计算移动平均
            self.stats.avg_access_time_ms = (
                (self.stats.avg_access_time_ms * (self.stats.total_requests - 1) + access_time) /
                self.stats.total_requests
            )


class RedisCacheBackend(CacheBackend):
    """Redis缓存后端"""
    
    def __init__(self, redis_config: Dict[str, Any]):
        """
        初始化Redis缓存
        
        Args:
            redis_config: Redis配置
        """
        if not REDIS_AVAILABLE:
            raise ImportError("Redis不可用")
        
        self.redis_config = redis_config
        self.redis_client = redis.Redis(**redis_config)
        self.stats = CacheStats()
        self._lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # 测试连接
        try:
            self.redis_client.ping()
            self.logger.info("Redis连接成功")
        except Exception as e:
            self.logger.error(f"Redis连接失败: {e}")
            raise
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        start_time = time.time()
        
        try:
            with self._lock:
                self.stats.total_requests += 1
            
            data = self.redis_client.get(key)
            
            if data is None:
                with self._lock:
                    self.stats.cache_misses += 1
                self._update_avg_access_time(start_time)
                return None
            
            # 反序列化
            value = pickle.loads(data)
            
            with self._lock:
                self.stats.cache_hits += 1
            self._update_avg_access_time(start_time)
            
            return value
            
        except Exception as e:
            self.logger.error(f"Redis获取失败: {e}")
            with self._lock:
                self.stats.cache_misses += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """设置缓存值"""
        try:
            # 序列化
            data = pickle.dumps(value)
            
            # 设置到Redis
            if ttl is not None:
                result = self.redis_client.setex(key, int(ttl), data)
            else:
                result = self.redis_client.set(key, data)
            
            return bool(result)
            
        except Exception as e:
            self.logger.error(f"Redis设置失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            result = self.redis_client.delete(key)
            return result > 0
        except Exception as e:
            self.logger.error(f"Redis删除失败: {e}")
            return False
    
    def clear(self) -> bool:
        """清空缓存"""
        try:
            self.redis_client.flushdb()
            return True
        except Exception as e:
            self.logger.error(f"Redis清空失败: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            self.logger.error(f"Redis检查存在失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            redis_info = self.redis_client.info()
            
            with self._lock:
                return {
                    'backend_type': 'redis',
                    'redis_version': redis_info.get('redis_version', 'unknown'),
                    'used_memory': redis_info.get('used_memory', 0),
                    'used_memory_human': redis_info.get('used_memory_human', '0B'),
                    'total_requests': self.stats.total_requests,
                    'cache_hits': self.stats.cache_hits,
                    'cache_misses': self.stats.cache_misses,
                    'hit_rate': self.stats.hit_rate,
                    'avg_access_time_ms': self.stats.avg_access_time_ms
                }
        except Exception as e:
            self.logger.error(f"获取Redis统计失败: {e}")
            return {'backend_type': 'redis', 'error': str(e)}
    
    def _update_avg_access_time(self, start_time: float):
        """更新平均访问时间"""
        access_time = (time.time() - start_time) * 1000
        with self._lock:
            if self.stats.total_requests == 1:
                self.stats.avg_access_time_ms = access_time
            else:
                self.stats.avg_access_time_ms = (
                    (self.stats.avg_access_time_ms * (self.stats.total_requests - 1) + access_time) /
                    self.stats.total_requests
                )


class EnhancedCache:
    """
    增强缓存系统
    
    功能特性：
    - 多层缓存架构（L1内存 + L2 Redis）
    - 智能缓存策略（LRU、TTL、访问频率）
    - 缓存预热和批量操作
    - 详细的性能统计
    - 缓存一致性保证
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化增强缓存
        
        Args:
            config: 缓存配置
        """
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)
        
        # 初始化缓存后端
        self.l1_cache = self._create_l1_cache()
        self.l2_cache = self._create_l2_cache()
        
        # 缓存策略
        self.enable_l2 = self.config.get('enable_l2_cache', False) and self.l2_cache is not None
        self.write_through = self.config.get('write_through', True)
        self.write_back = self.config.get('write_back', False)
        
        # 统计信息
        self.global_stats = CacheStats()
        self._stats_lock = threading.Lock()
        
        # 预热配置
        self.preload_functions: List[Callable] = []
        
        self.logger.info(f"增强缓存初始化完成，L2缓存: {'启用' if self.enable_l2 else '禁用'}")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'l1_cache': {
                'max_size': 1000,
                'default_ttl': 3600  # 1小时
            },
            'l2_cache': {
                'enabled': False,
                'redis_config': {
                    'host': 'localhost',
                    'port': 6379,
                    'db': 0,
                    'decode_responses': False
                }
            },
            'enable_l2_cache': False,
            'write_through': True,
            'write_back': False,
            'preload_enabled': True,
            'stats_enabled': True
        }
    
    def _create_l1_cache(self) -> MemoryCacheBackend:
        """创建L1缓存（内存）"""
        l1_config = self.config.get('l1_cache', {})
        return MemoryCacheBackend(
            max_size=l1_config.get('max_size', 1000),
            default_ttl=l1_config.get('default_ttl', 3600)
        )
    
    def _create_l2_cache(self) -> Optional[RedisCacheBackend]:
        """创建L2缓存（Redis）"""
        l2_config = self.config.get('l2_cache', {})
        
        if not l2_config.get('enabled', False) or not REDIS_AVAILABLE:
            return None
        
        try:
            redis_config = l2_config.get('redis_config', {})
            return RedisCacheBackend(redis_config)
        except Exception as e:
            self.logger.warning(f"L2缓存初始化失败: {e}")
            return None
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值或None
        """
        start_time = time.time()
        
        with self._stats_lock:
            self.global_stats.total_requests += 1
        
        # 先尝试L1缓存
        value = self.l1_cache.get(key)
        if value is not None:
            with self._stats_lock:
                self.global_stats.cache_hits += 1
            self._update_global_avg_access_time(start_time)
            return value
        
        # 如果启用L2缓存，尝试L2
        if self.enable_l2 and self.l2_cache:
            value = self.l2_cache.get(key)
            if value is not None:
                # L2命中，回写到L1
                self.l1_cache.set(key, value)
                with self._stats_lock:
                    self.global_stats.cache_hits += 1
                self._update_global_avg_access_time(start_time)
                return value
        
        # 缓存未命中
        with self._stats_lock:
            self.global_stats.cache_misses += 1
        self._update_global_avg_access_time(start_time)
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            
        Returns:
            是否设置成功
        """
        success = True
        
        # 设置到L1缓存
        if not self.l1_cache.set(key, value, ttl):
            success = False
        
        # 如果启用L2缓存且使用直写策略
        if self.enable_l2 and self.l2_cache and self.write_through:
            if not self.l2_cache.set(key, value, ttl):
                success = False
        
        return success
    
    def delete(self, key: str) -> bool:
        """
        删除缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        success = True
        
        # 从L1缓存删除
        if not self.l1_cache.delete(key):
            success = False
        
        # 从L2缓存删除
        if self.enable_l2 and self.l2_cache:
            if not self.l2_cache.delete(key):
                success = False
        
        return success
    
    def clear(self) -> bool:
        """
        清空所有缓存
        
        Returns:
            是否清空成功
        """
        success = True
        
        # 清空L1缓存
        if not self.l1_cache.clear():
            success = False
        
        # 清空L2缓存
        if self.enable_l2 and self.l2_cache:
            if not self.l2_cache.clear():
                success = False
        
        return success
    
    def exists(self, key: str) -> bool:
        """
        检查键是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        # 检查L1缓存
        if self.l1_cache.exists(key):
            return True
        
        # 检查L2缓存
        if self.enable_l2 and self.l2_cache:
            return self.l2_cache.exists(key)
        
        return False
    
    def get_or_set(self, key: str, factory: Callable[[], Any], 
                   ttl: Optional[float] = None) -> Any:
        """
        获取缓存值，如果不存在则通过工厂函数创建
        
        Args:
            key: 缓存键
            factory: 值工厂函数
            ttl: 生存时间
            
        Returns:
            缓存值
        """
        value = self.get(key)
        if value is not None:
            return value
        
        # 缓存未命中，通过工厂函数创建
        value = factory()
        self.set(key, value, ttl)
        return value
    
    def batch_get(self, keys: List[str]) -> Dict[str, Any]:
        """
        批量获取缓存值
        
        Args:
            keys: 缓存键列表
            
        Returns:
            键值对字典
        """
        result = {}
        for key in keys:
            value = self.get(key)
            if value is not None:
                result[key] = value
        return result
    
    def batch_set(self, items: Dict[str, Any], ttl: Optional[float] = None) -> bool:
        """
        批量设置缓存值
        
        Args:
            items: 键值对字典
            ttl: 生存时间
            
        Returns:
            是否全部设置成功
        """
        success = True
        for key, value in items.items():
            if not self.set(key, value, ttl):
                success = False
        return success
    
    def preload(self, preload_data: Optional[Dict[str, Any]] = None):
        """
        缓存预热
        
        Args:
            preload_data: 预加载数据，None表示使用注册的预加载函数
        """
        if not self.config.get('preload_enabled', True):
            return
        
        self.logger.info("开始缓存预热...")
        
        if preload_data:
            # 使用提供的数据预热
            self.batch_set(preload_data)
            self.logger.info(f"预热了 {len(preload_data)} 个缓存项")
        else:
            # 使用注册的预加载函数
            for preload_func in self.preload_functions:
                try:
                    data = preload_func()
                    if isinstance(data, dict):
                        self.batch_set(data)
                        self.logger.info(f"预热了 {len(data)} 个缓存项")
                except Exception as e:
                    self.logger.error(f"预热函数执行失败: {e}")
    
    def register_preload_function(self, func: Callable[[], Dict[str, Any]]):
        """
        注册预加载函数
        
        Args:
            func: 返回键值对字典的函数
        """
        self.preload_functions.append(func)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._stats_lock:
            stats = {
                'global_stats': {
                    'total_requests': self.global_stats.total_requests,
                    'cache_hits': self.global_stats.cache_hits,
                    'cache_misses': self.global_stats.cache_misses,
                    'hit_rate': self.global_stats.hit_rate,
                    'avg_access_time_ms': self.global_stats.avg_access_time_ms
                },
                'l1_cache_stats': self.l1_cache.get_stats(),
                'l2_cache_enabled': self.enable_l2
            }
            
            if self.enable_l2 and self.l2_cache:
                stats['l2_cache_stats'] = self.l2_cache.get_stats()
            
            return stats
    
    def _update_global_avg_access_time(self, start_time: float):
        """更新全局平均访问时间"""
        access_time = (time.time() - start_time) * 1000
        with self._stats_lock:
            if self.global_stats.total_requests == 1:
                self.global_stats.avg_access_time_ms = access_time
            else:
                self.global_stats.avg_access_time_ms = (
                    (self.global_stats.avg_access_time_ms * (self.global_stats.total_requests - 1) + access_time) /
                    self.global_stats.total_requests
                )


def create_enhanced_cache(config: Optional[Dict[str, Any]] = None) -> EnhancedCache:
    """
    创建增强缓存实例
    
    Args:
        config: 缓存配置
        
    Returns:
        增强缓存实例
    """
    return EnhancedCache(config)


def test_enhanced_cache():
    """测试增强缓存"""
    print("=== 测试增强缓存 ===")
    
    # 创建缓存实例
    cache = create_enhanced_cache({
        'l1_cache': {'max_size': 100, 'default_ttl': 60},
        'enable_l2_cache': False  # 测试时禁用Redis
    })
    
    print("1. 基础缓存操作测试")
    
    # 设置缓存
    cache.set("test_key", "test_value", 30)
    print(f"  设置缓存: test_key = test_value")
    
    # 获取缓存
    value = cache.get("test_key")
    print(f"  获取缓存: test_key = {value}")
    
    # 批量操作
    batch_data = {
        "key1": "value1",
        "key2": "value2",
        "key3": "value3"
    }
    cache.batch_set(batch_data, 60)
    print(f"  批量设置: {len(batch_data)} 个键值对")
    
    batch_result = cache.batch_get(["key1", "key2", "key3", "key4"])
    print(f"  批量获取: {len(batch_result)} 个值")
    
    print("\n2. 缓存统计测试")
    stats = cache.get_stats()
    print(f"  总请求数: {stats['global_stats']['total_requests']}")
    print(f"  缓存命中数: {stats['global_stats']['cache_hits']}")
    print(f"  命中率: {stats['global_stats']['hit_rate']:.1f}%")
    print(f"  L1缓存大小: {stats['l1_cache_stats']['cache_size']}")
    
    print("\n3. 工厂函数测试")
    
    def create_expensive_value():
        time.sleep(0.01)  # 模拟耗时操作
        return "expensive_value"
    
    # 第一次调用，会执行工厂函数
    start_time = time.time()
    value1 = cache.get_or_set("expensive_key", create_expensive_value, 60)
    time1 = (time.time() - start_time) * 1000
    
    # 第二次调用，从缓存获取
    start_time = time.time()
    value2 = cache.get_or_set("expensive_key", create_expensive_value, 60)
    time2 = (time.time() - start_time) * 1000
    
    print(f"  第一次调用: {value1}, 耗时: {time1:.2f}ms")
    print(f"  第二次调用: {value2}, 耗时: {time2:.2f}ms")
    print(f"  性能提升: {time1/time2:.1f}倍")
    
    print("\n✓ 增强缓存测试完成")


if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    test_enhanced_cache()
