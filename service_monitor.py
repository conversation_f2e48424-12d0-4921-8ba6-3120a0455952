"""
服务监控系统
提供全面的微服务监控、告警、性能分析功能
采用保守的实施策略，支持多种监控指标和告警方式

设计原则：
1. 全面监控：服务健康、性能指标、业务指标
2. 智能告警：多级告警、告警聚合、自动恢复
3. 可视化展示：实时图表、历史趋势、性能分析
4. 轻量级实现：无外部依赖，易于部署
5. 可扩展性：支持自定义指标和告警规则
"""

import time
import json
import threading
import logging
import statistics
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from collections import deque, defaultdict
from dataclasses import dataclass, asdict
from enum import Enum
import os


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"      # 计数器
    GAUGE = "gauge"         # 仪表盘
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"         # 计时器


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: float
    value: float
    labels: Dict[str, str] = None
    
    def __post_init__(self):
        if self.labels is None:
            self.labels = {}


@dataclass
class Alert:
    """告警信息"""
    id: str
    level: AlertLevel
    service_name: str
    metric_name: str
    message: str
    timestamp: float
    value: float
    threshold: float
    resolved: bool = False
    resolved_timestamp: Optional[float] = None


class Metric:
    """指标基类"""
    
    def __init__(self, name: str, metric_type: MetricType, 
                 description: str = "", labels: Dict[str, str] = None):
        """
        初始化指标
        
        Args:
            name: 指标名称
            metric_type: 指标类型
            description: 指标描述
            labels: 标签
        """
        self.name = name
        self.type = metric_type
        self.description = description
        self.labels = labels or {}
        self.data_points: deque = deque(maxlen=1000)  # 保留最近1000个数据点
        self._lock = threading.Lock()
    
    def add_point(self, value: float, timestamp: Optional[float] = None, 
                  labels: Dict[str, str] = None):
        """添加数据点"""
        if timestamp is None:
            timestamp = time.time()
        
        point = MetricPoint(
            timestamp=timestamp,
            value=value,
            labels={**self.labels, **(labels or {})}
        )
        
        with self._lock:
            self.data_points.append(point)
    
    def get_latest_value(self) -> Optional[float]:
        """获取最新值"""
        with self._lock:
            if self.data_points:
                return self.data_points[-1].value
            return None
    
    def get_values_in_range(self, start_time: float, end_time: float) -> List[MetricPoint]:
        """获取时间范围内的值"""
        with self._lock:
            return [
                point for point in self.data_points
                if start_time <= point.timestamp <= end_time
            ]
    
    def get_statistics(self, duration_seconds: int = 300) -> Dict[str, float]:
        """获取统计信息"""
        end_time = time.time()
        start_time = end_time - duration_seconds
        
        points = self.get_values_in_range(start_time, end_time)
        if not points:
            return {}
        
        values = [point.value for point in points]
        
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'avg': statistics.mean(values),
            'median': statistics.median(values),
            'std': statistics.stdev(values) if len(values) > 1 else 0.0,
            'sum': sum(values)
        }


class Counter(Metric):
    """计数器指标"""
    
    def __init__(self, name: str, description: str = "", labels: Dict[str, str] = None):
        super().__init__(name, MetricType.COUNTER, description, labels)
        self._value = 0.0
        self._value_lock = threading.Lock()
    
    def increment(self, amount: float = 1.0, labels: Dict[str, str] = None):
        """增加计数"""
        with self._value_lock:
            self._value += amount
            self.add_point(self._value, labels=labels)
    
    def get_value(self) -> float:
        """获取当前值"""
        with self._value_lock:
            return self._value


class Gauge(Metric):
    """仪表盘指标"""
    
    def __init__(self, name: str, description: str = "", labels: Dict[str, str] = None):
        super().__init__(name, MetricType.GAUGE, description, labels)
        self._value = 0.0
        self._value_lock = threading.Lock()
    
    def set(self, value: float, labels: Dict[str, str] = None):
        """设置值"""
        with self._value_lock:
            self._value = value
            self.add_point(value, labels=labels)
    
    def increment(self, amount: float = 1.0, labels: Dict[str, str] = None):
        """增加值"""
        with self._value_lock:
            self._value += amount
            self.add_point(self._value, labels=labels)
    
    def decrement(self, amount: float = 1.0, labels: Dict[str, str] = None):
        """减少值"""
        self.increment(-amount, labels)
    
    def get_value(self) -> float:
        """获取当前值"""
        with self._value_lock:
            return self._value


class Timer(Metric):
    """计时器指标"""
    
    def __init__(self, name: str, description: str = "", labels: Dict[str, str] = None):
        super().__init__(name, MetricType.TIMER, description, labels)
    
    def time(self, labels: Dict[str, str] = None):
        """计时上下文管理器"""
        return TimerContext(self, labels)
    
    def record(self, duration_seconds: float, labels: Dict[str, str] = None):
        """记录时间"""
        self.add_point(duration_seconds * 1000, labels=labels)  # 转换为毫秒


class TimerContext:
    """计时器上下文"""
    
    def __init__(self, timer: Timer, labels: Dict[str, str] = None):
        self.timer = timer
        self.labels = labels
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.timer.record(duration, self.labels)


class AlertRule:
    """告警规则"""
    
    def __init__(self, name: str, metric_name: str, condition: str, 
                 threshold: float, level: AlertLevel, 
                 duration_seconds: int = 60, message: str = ""):
        """
        初始化告警规则
        
        Args:
            name: 规则名称
            metric_name: 指标名称
            condition: 条件（>, <, >=, <=, ==, !=）
            threshold: 阈值
            level: 告警级别
            duration_seconds: 持续时间
            message: 告警消息模板
        """
        self.name = name
        self.metric_name = metric_name
        self.condition = condition
        self.threshold = threshold
        self.level = level
        self.duration_seconds = duration_seconds
        self.message = message or f"{metric_name} {condition} {threshold}"
        
        # 状态跟踪
        self.triggered_time: Optional[float] = None
        self.last_alert_time: Optional[float] = None
        self.active_alert: Optional[Alert] = None
    
    def evaluate(self, metric: Metric, service_name: str) -> Optional[Alert]:
        """评估告警规则"""
        current_value = metric.get_latest_value()
        if current_value is None:
            return None
        
        current_time = time.time()
        is_triggered = self._check_condition(current_value)
        
        if is_triggered:
            if self.triggered_time is None:
                self.triggered_time = current_time
            
            # 检查是否达到持续时间
            if current_time - self.triggered_time >= self.duration_seconds:
                # 避免重复告警
                if (self.last_alert_time is None or 
                    current_time - self.last_alert_time > 300):  # 5分钟内不重复告警
                    
                    alert = Alert(
                        id=f"{service_name}_{self.name}_{int(current_time)}",
                        level=self.level,
                        service_name=service_name,
                        metric_name=self.metric_name,
                        message=self.message.format(
                            value=current_value,
                            threshold=self.threshold,
                            service=service_name
                        ),
                        timestamp=current_time,
                        value=current_value,
                        threshold=self.threshold
                    )
                    
                    self.last_alert_time = current_time
                    self.active_alert = alert
                    return alert
        else:
            # 条件不满足，重置状态
            if self.triggered_time is not None:
                self.triggered_time = None
                
                # 如果有活跃告警，标记为已解决
                if self.active_alert and not self.active_alert.resolved:
                    self.active_alert.resolved = True
                    self.active_alert.resolved_timestamp = current_time
        
        return None
    
    def _check_condition(self, value: float) -> bool:
        """检查条件"""
        if self.condition == ">":
            return value > self.threshold
        elif self.condition == "<":
            return value < self.threshold
        elif self.condition == ">=":
            return value >= self.threshold
        elif self.condition == "<=":
            return value <= self.threshold
        elif self.condition == "==":
            return abs(value - self.threshold) < 1e-9
        elif self.condition == "!=":
            return abs(value - self.threshold) >= 1e-9
        else:
            return False


class ServiceMonitor:
    """
    服务监控器
    
    功能特性：
    - 多种指标类型支持
    - 灵活的告警规则
    - 实时监控和历史数据
    - 性能统计和分析
    - 可扩展的告警通知
    """
    
    def __init__(self, service_name: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化服务监控器
        
        Args:
            service_name: 服务名称
            config: 监控配置
        """
        self.service_name = service_name
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)
        
        # 指标存储
        self.metrics: Dict[str, Metric] = {}
        self.alert_rules: Dict[str, AlertRule] = {}
        self.alerts: List[Alert] = []
        
        # 线程安全
        self._metrics_lock = threading.Lock()
        self._alerts_lock = threading.Lock()
        
        # 监控线程
        self.monitoring_enabled = True
        self.monitoring_thread = None
        
        # 告警回调
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # 启动监控
        self._start_monitoring()
        
        self.logger.info(f"服务监控器初始化完成: {service_name}")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'monitoring_interval': 10,  # 监控间隔（秒）
            'max_alerts': 1000,        # 最大告警数量
            'alert_retention_days': 7,  # 告警保留天数
            'enable_file_output': True, # 启用文件输出
            'output_directory': './monitoring_data'
        }
    
    def register_counter(self, name: str, description: str = "", 
                        labels: Dict[str, str] = None) -> Counter:
        """注册计数器指标"""
        counter = Counter(name, description, labels)
        with self._metrics_lock:
            self.metrics[name] = counter
        return counter
    
    def register_gauge(self, name: str, description: str = "", 
                      labels: Dict[str, str] = None) -> Gauge:
        """注册仪表盘指标"""
        gauge = Gauge(name, description, labels)
        with self._metrics_lock:
            self.metrics[name] = gauge
        return gauge
    
    def register_timer(self, name: str, description: str = "", 
                      labels: Dict[str, str] = None) -> Timer:
        """注册计时器指标"""
        timer = Timer(name, description, labels)
        with self._metrics_lock:
            self.metrics[name] = timer
        return timer
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则"""
        with self._alerts_lock:
            self.alert_rules[rule.name] = rule
        self.logger.info(f"添加告警规则: {rule.name}")
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def get_metric(self, name: str) -> Optional[Metric]:
        """获取指标"""
        with self._metrics_lock:
            return self.metrics.get(name)
    
    def get_all_metrics(self) -> Dict[str, Metric]:
        """获取所有指标"""
        with self._metrics_lock:
            return self.metrics.copy()
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        with self._alerts_lock:
            return [alert for alert in self.alerts if not alert.resolved]
    
    def get_all_alerts(self) -> List[Alert]:
        """获取所有告警"""
        with self._alerts_lock:
            return self.alerts.copy()
    
    def get_service_health(self) -> Dict[str, Any]:
        """获取服务健康状态"""
        active_alerts = self.get_active_alerts()
        
        # 计算健康分数
        health_score = 100.0
        for alert in active_alerts:
            if alert.level == AlertLevel.CRITICAL:
                health_score -= 30
            elif alert.level == AlertLevel.ERROR:
                health_score -= 20
            elif alert.level == AlertLevel.WARNING:
                health_score -= 10
            elif alert.level == AlertLevel.INFO:
                health_score -= 5
        
        health_score = max(0, health_score)
        
        # 确定健康状态
        if health_score >= 90:
            status = "healthy"
        elif health_score >= 70:
            status = "warning"
        elif health_score >= 50:
            status = "degraded"
        else:
            status = "unhealthy"
        
        return {
            'service_name': self.service_name,
            'status': status,
            'health_score': health_score,
            'active_alerts_count': len(active_alerts),
            'critical_alerts': len([a for a in active_alerts if a.level == AlertLevel.CRITICAL]),
            'error_alerts': len([a for a in active_alerts if a.level == AlertLevel.ERROR]),
            'warning_alerts': len([a for a in active_alerts if a.level == AlertLevel.WARNING]),
            'timestamp': time.time()
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            'service_name': self.service_name,
            'timestamp': time.time(),
            'metrics_count': len(self.metrics),
            'alerts_count': len(self.alerts),
            'active_alerts_count': len(self.get_active_alerts()),
            'metrics': {}
        }
        
        # 收集各指标的统计信息
        for name, metric in self.get_all_metrics().items():
            stats = metric.get_statistics(300)  # 最近5分钟
            if stats:
                summary['metrics'][name] = {
                    'type': metric.type.value,
                    'latest_value': metric.get_latest_value(),
                    'statistics': stats
                }
        
        return summary
    
    def export_data(self, format: str = "json") -> str:
        """导出监控数据"""
        data = {
            'service_name': self.service_name,
            'export_time': datetime.now().isoformat(),
            'health': self.get_service_health(),
            'performance': self.get_performance_summary(),
            'alerts': [asdict(alert) for alert in self.get_all_alerts()],
            'metrics': {}
        }
        
        # 导出指标数据
        for name, metric in self.get_all_metrics().items():
            data['metrics'][name] = {
                'name': metric.name,
                'type': metric.type.value,
                'description': metric.description,
                'labels': metric.labels,
                'data_points': [asdict(point) for point in metric.data_points]
            }
        
        if format == "json":
            return json.dumps(data, indent=2, default=str)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def _start_monitoring(self):
        """启动监控线程"""
        if self.monitoring_thread is None or not self.monitoring_thread.is_alive():
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
    
    def _monitoring_loop(self):
        """监控循环"""
        interval = self.config.get('monitoring_interval', 10)
        
        while self.monitoring_enabled:
            try:
                self._check_alerts()
                self._cleanup_old_data()
                time.sleep(interval)
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(interval)
    
    def _check_alerts(self):
        """检查告警"""
        with self._alerts_lock:
            for rule in self.alert_rules.values():
                metric = self.get_metric(rule.metric_name)
                if metric:
                    alert = rule.evaluate(metric, self.service_name)
                    if alert:
                        self.alerts.append(alert)
                        self._trigger_alert_callbacks(alert)
    
    def _trigger_alert_callbacks(self, alert: Alert):
        """触发告警回调"""
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调执行失败: {e}")
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        current_time = time.time()
        retention_seconds = self.config.get('alert_retention_days', 7) * 24 * 3600
        
        with self._alerts_lock:
            # 清理旧告警
            self.alerts = [
                alert for alert in self.alerts
                if current_time - alert.timestamp < retention_seconds
            ]
            
            # 限制告警数量
            max_alerts = self.config.get('max_alerts', 1000)
            if len(self.alerts) > max_alerts:
                self.alerts = self.alerts[-max_alerts:]
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_enabled = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)


def create_service_monitor(service_name: str, 
                          config: Optional[Dict[str, Any]] = None) -> ServiceMonitor:
    """
    创建服务监控器
    
    Args:
        service_name: 服务名称
        config: 监控配置
        
    Returns:
        服务监控器实例
    """
    return ServiceMonitor(service_name, config)


# 便捷装饰器
def monitor_performance(monitor: ServiceMonitor, metric_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        name = metric_name or f"{func.__name__}_duration"
        timer = monitor.register_timer(name, f"执行时间: {func.__name__}")
        
        def wrapper(*args, **kwargs):
            with timer.time():
                return func(*args, **kwargs)
        return wrapper
    return decorator


def test_service_monitor():
    """测试服务监控"""
    print("=== 测试服务监控 ===")
    
    # 创建监控器
    monitor = create_service_monitor("test-service", {
        'monitoring_interval': 1,  # 1秒检查一次
        'max_alerts': 100
    })
    
    print("1. 注册指标")
    
    # 注册指标
    request_counter = monitor.register_counter("requests_total", "总请求数")
    response_time = monitor.register_timer("response_time", "响应时间")
    cpu_usage = monitor.register_gauge("cpu_usage", "CPU使用率")
    
    print(f"  注册了 {len(monitor.get_all_metrics())} 个指标")
    
    print("\n2. 添加告警规则")
    
    # 添加告警规则
    cpu_alert = AlertRule(
        name="high_cpu",
        metric_name="cpu_usage",
        condition=">",
        threshold=80.0,
        level=AlertLevel.WARNING,
        duration_seconds=2,
        message="CPU使用率过高: {value:.1f}%"
    )
    monitor.add_alert_rule(cpu_alert)
    
    print("  添加了CPU使用率告警规则")
    
    print("\n3. 模拟指标数据")
    
    # 模拟一些数据
    for i in range(5):
        request_counter.increment()
        
        with response_time.time():
            time.sleep(0.01)  # 模拟处理时间
        
        # 模拟CPU使用率变化
        cpu_value = 50 + i * 10  # 50%, 60%, 70%, 80%, 90%
        cpu_usage.set(cpu_value)
        
        print(f"  请求 {i+1}: CPU {cpu_value}%, 响应时间已记录")
        time.sleep(0.5)
    
    print("\n4. 检查健康状态")
    health = monitor.get_service_health()
    print(f"  服务状态: {health['status']}")
    print(f"  健康分数: {health['health_score']}")
    print(f"  活跃告警: {health['active_alerts_count']}")
    
    print("\n5. 性能摘要")
    summary = monitor.get_performance_summary()
    print(f"  指标数量: {summary['metrics_count']}")
    for name, info in summary['metrics'].items():
        latest = info.get('latest_value', 0)
        print(f"  {name}: {latest:.2f}")
    
    # 停止监控
    monitor.stop_monitoring()
    
    print("\n✓ 服务监控测试完成")


if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_service_monitor()
