"""
高级SQLite管理器功能测试
测试分片功能、数据操作、性能统计等核心功能
"""

import sys
import os
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_basic_operations():
    """测试基础数据操作"""
    print("=== 测试基础数据操作 ===")
    
    from advanced_sqlite_manager import AdvancedSQLiteManager, ShardConfig, ShardingStrategy
    
    # 使用时间分片策略
    config = ShardConfig(
        strategy=ShardingStrategy.TIME_BASED,
        time_unit="year",
        auto_create_shards=True
    )
    
    manager = AdvancedSQLiteManager("test_phone_marks.db", config)
    
    # 测试数据
    test_records = [
        {
            'phone_number': '13800138000',
            'mark_info': '推销电话',
            'province': '北京',
            'city': '北京',
            'isp': '中国移动',
            'location_source': 'database',
            'phone_model': 'Test Phone 1',
            'android_version': '10',
            'status': 'success',
            'processing_time': 2.5,
            'method': 'ML Kit',
            'retry_count': 0,
            'dial_time': datetime.now().isoformat()
        },
        {
            'phone_number': '13800138001',
            'mark_info': '快递通知',
            'province': '广东',
            'city': '深圳',
            'isp': '中国联通',
            'location_source': 'database',
            'phone_model': 'Test Phone 2',
            'android_version': '11',
            'status': 'success',
            'processing_time': 1.8,
            'method': 'ML Kit',
            'retry_count': 0,
            'dial_time': (datetime.now() - timedelta(days=1)).isoformat()
        },
        {
            'phone_number': '13800138002',
            'mark_info': '诈骗电话',
            'province': '上海',
            'city': '上海',
            'isp': '中国电信',
            'location_source': 'ocr',
            'phone_model': 'Test Phone 3',
            'android_version': '12',
            'status': 'success',
            'processing_time': 3.2,
            'method': 'ML Kit',
            'retry_count': 1,
            'dial_time': (datetime.now() - timedelta(days=365)).isoformat()  # 去年的数据
        }
    ]
    
    print(f"\n1. 保存测试记录")
    success_count = 0
    for i, record in enumerate(test_records, 1):
        success = manager.save_dial_record(record)
        if success:
            success_count += 1
            print(f"  ✓ 记录 {i} 保存成功: {record['phone_number']}")
        else:
            print(f"  ✗ 记录 {i} 保存失败: {record['phone_number']}")
    
    print(f"保存成功率: {success_count}/{len(test_records)}")
    
    print(f"\n2. 查询最近记录")
    recent_records = manager.get_recent_records(5)
    print(f"查询到 {len(recent_records)} 条最近记录")
    for record in recent_records:
        print(f"  - {record['phone_number']}: {record.get('mark_info', 'N/A')} ({record.get('dial_time', 'N/A')})")
    
    print(f"\n3. 根据号码查询")
    phone_records = manager.get_records_by_phone('13800138000')
    print(f"号码 13800138000 的记录数: {len(phone_records)}")
    
    print(f"\n4. 获取统计信息")
    stats = manager.get_statistics()
    print(f"总记录数: {stats.get('total_records', 0)}")
    print(f"标记类型数: {len(stats.get('mark_distribution', {}))}")
    print(f"省份数: {len(stats.get('province_distribution', {}))}")
    
    print(f"\n5. 性能统计")
    perf_stats = manager.get_performance_stats()
    print(f"总查询数: {perf_stats['total_queries']}")
    print(f"平均查询时间: {perf_stats['avg_query_time']:.2f}ms")
    print(f"慢查询率: {perf_stats['slow_query_rate']:.1f}%")
    
    print(f"\n6. 分片信息")
    shard_info = manager.get_shard_info()
    print(f"分片策略: {shard_info['strategy']}")
    print(f"分片数量: {shard_info['total_shards']}")
    for shard_name, info in shard_info['shards'].items():
        if info.get('exists', False):
            print(f"  - {shard_name}: {info['record_count']} 条记录, {info['file_size_mb']}MB")
    
    # 清理连接
    manager.close_all_connections()
    
    return success_count == len(test_records)

def test_cross_shard_queries():
    """测试跨分片查询"""
    print("\n=== 测试跨分片查询 ===")
    
    from advanced_sqlite_manager import AdvancedSQLiteManager, ShardConfig, ShardingStrategy
    
    # 使用混合分片策略
    config = ShardConfig(
        strategy=ShardingStrategy.HYBRID,
        enable_cross_shard_queries=True
    )
    
    manager = AdvancedSQLiteManager("test_phone_marks.db", config)
    
    # 添加不同年份的测试数据
    test_records = [
        {
            'phone_number': '13900139000',
            'mark_info': '2025年记录',
            'province': '江苏',
            'city': '南京',
            'dial_time': datetime(2025, 1, 1).isoformat()
        },
        {
            'phone_number': '13900139001',
            'mark_info': '2024年记录',
            'province': '浙江',
            'city': '杭州',
            'dial_time': datetime(2024, 6, 15).isoformat()
        }
    ]
    
    print("1. 保存跨年份数据")
    for record in test_records:
        success = manager.save_dial_record(record)
        print(f"  {'✓' if success else '✗'} {record['phone_number']}: {record['mark_info']}")
    
    print("\n2. 跨分片查询所有记录")
    all_records = manager.get_recent_records(10)
    print(f"跨分片查询到 {len(all_records)} 条记录")
    
    # 按年份分组显示
    records_by_year = {}
    for record in all_records:
        dial_time = record.get('dial_time', '')
        if dial_time:
            year = dial_time[:4]
            if year not in records_by_year:
                records_by_year[year] = []
            records_by_year[year].append(record)
    
    for year, records in sorted(records_by_year.items()):
        print(f"  {year}年: {len(records)} 条记录")
    
    print("\n3. 跨分片统计信息")
    stats = manager.get_statistics()
    print(f"总记录数: {stats.get('total_records', 0)}")
    print(f"省份分布: {list(stats.get('province_distribution', {}).keys())}")
    
    # 清理连接
    manager.close_all_connections()
    
    return len(all_records) > 0

def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 测试性能对比 ===")
    
    from advanced_sqlite_manager import AdvancedSQLiteManager, ShardConfig, ShardingStrategy
    
    # 测试数据
    test_records = []
    for i in range(100):
        record = {
            'phone_number': f'1380013{i:04d}',
            'mark_info': f'测试记录{i}',
            'province': '测试省',
            'city': '测试市',
            'phone_model': 'Test Phone',
            'android_version': '10',
            'status': 'success',
            'processing_time': 1.0,
            'method': 'ML Kit'
        }
        test_records.append(record)
    
    # 测试传统模式
    print("1. 传统模式性能测试")
    config_none = ShardConfig(strategy=ShardingStrategy.NONE)
    manager_none = AdvancedSQLiteManager("test_traditional.db", config_none)
    
    start_time = time.time()
    for record in test_records:
        manager_none.save_dial_record(record)
    traditional_write_time = time.time() - start_time
    
    start_time = time.time()
    records = manager_none.get_recent_records(50)
    traditional_read_time = time.time() - start_time
    
    print(f"  写入时间: {traditional_write_time:.3f}秒")
    print(f"  读取时间: {traditional_read_time:.3f}秒")
    print(f"  读取记录数: {len(records)}")
    
    # 测试分片模式
    print("\n2. 分片模式性能测试")
    config_shard = ShardConfig(strategy=ShardingStrategy.FUNCTIONAL)
    manager_shard = AdvancedSQLiteManager("test_sharded.db", config_shard)
    
    start_time = time.time()
    for record in test_records:
        manager_shard.save_dial_record(record)
    sharded_write_time = time.time() - start_time
    
    start_time = time.time()
    records = manager_shard.get_recent_records(50)
    sharded_read_time = time.time() - start_time
    
    print(f"  写入时间: {sharded_write_time:.3f}秒")
    print(f"  读取时间: {sharded_read_time:.3f}秒")
    print(f"  读取记录数: {len(records)}")
    
    # 性能对比
    print("\n3. 性能对比结果")
    write_improvement = ((traditional_write_time - sharded_write_time) / traditional_write_time) * 100
    read_improvement = ((traditional_read_time - sharded_read_time) / traditional_read_time) * 100
    
    print(f"  写入性能变化: {write_improvement:+.1f}%")
    print(f"  读取性能变化: {read_improvement:+.1f}%")
    
    # 获取详细性能统计
    print("\n4. 详细性能统计")
    perf_stats_traditional = manager_none.get_performance_stats()
    perf_stats_sharded = manager_shard.get_performance_stats()
    
    print("传统模式:")
    print(f"  平均查询时间: {perf_stats_traditional['avg_query_time']:.2f}ms")
    print(f"  慢查询率: {perf_stats_traditional['slow_query_rate']:.1f}%")
    
    print("分片模式:")
    print(f"  平均查询时间: {perf_stats_sharded['avg_query_time']:.2f}ms")
    print(f"  慢查询率: {perf_stats_sharded['slow_query_rate']:.1f}%")
    print(f"  分片数量: {perf_stats_sharded['sharding_info']['total_shards']}")
    
    # 清理连接
    manager_none.close_all_connections()
    manager_shard.close_all_connections()
    
    return True

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    from advanced_sqlite_manager import AdvancedSQLiteManager, ShardConfig, ShardingStrategy
    
    # 测试无效配置的处理
    print("1. 测试错误恢复机制")
    
    try:
        # 创建一个可能失败的配置
        config = ShardConfig(strategy=ShardingStrategy.TIME_BASED)
        manager = AdvancedSQLiteManager("/invalid/path/test.db", config)
        
        # 尝试保存记录
        test_record = {
            'phone_number': '13800138999',
            'mark_info': '错误测试',
            'phone_model': 'Test Phone',
            'android_version': '10'
        }
        
        success = manager.save_dial_record(test_record)
        print(f"  错误情况下保存记录: {'成功' if success else '失败（预期）'}")
        
        # 尝试查询记录
        records = manager.get_recent_records(5)
        print(f"  错误情况下查询记录: {len(records)} 条")
        
        manager.close_all_connections()
        
    except Exception as e:
        print(f"  捕获异常: {type(e).__name__}: {e}")
    
    print("✓ 错误处理测试完成")
    return True

def main():
    """主测试函数"""
    print("开始高级SQLite管理器功能测试...\n")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_basic_operations())
    test_results.append(test_cross_shard_queries())
    test_results.append(test_performance_comparison())
    test_results.append(test_error_handling())
    
    # 总结测试结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"\n=== 测试结果总结 ===")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 高级SQLite管理器所有测试通过！")
        print("\n✅ 验证的功能:")
        print("  1. 多种分片策略（时间、功能、混合）")
        print("  2. 智能查询路由")
        print("  3. 跨分片查询")
        print("  4. 向后兼容性")
        print("  5. 性能监控和统计")
        print("  6. 错误处理和恢复")
        
        print("\n📈 性能特点:")
        print("  - 支持自动分片管理")
        print("  - 智能查询路由优化")
        print("  - 完整的性能监控")
        print("  - 保持API兼容性")
        
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
