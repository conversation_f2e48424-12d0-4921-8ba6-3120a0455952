"""
Web后台管理系统
提供完整的Web界面用于管理电话号码标记识别系统
支持用户管理、权限控制、数据导入导出、统计分析等功能

功能特性：
1. RBAC权限管理
2. 批量数据导入和处理
3. 结果导出和统计分析
4. 设备管理和状态监控
5. 历史数据管理和训练
6. 系统日志和审计
"""

import os
import logging
import time
import json
import hashlib
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

# Web框架
try:
    from fastapi import FastAPI, HTTPException, Depends, Request, Form, File, UploadFile
    from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.staticfiles import StaticFiles
    from fastapi.templating import Jinja2Templates
    from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
    from pydantic import BaseModel, Field
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI不可用，Web管理系统功能受限")

# 数据库
try:
    import sqlite3
    from advanced_sqlite_manager import AdvancedSQLiteManager, ShardConfig, ShardingStrategy
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    logging.warning("数据库模块不可用")

# JWT认证
try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False
    logging.warning("JWT不可用，将使用简单认证")


class UserRole(Enum):
    """用户角色"""
    ADMIN = "admin"          # 管理员
    OPERATOR = "operator"    # 操作员
    VIEWER = "viewer"        # 查看者


class Permission(Enum):
    """权限"""
    USER_MANAGE = "user_manage"           # 用户管理
    DATA_IMPORT = "data_import"           # 数据导入
    DATA_EXPORT = "data_export"           # 数据导出
    DEVICE_MANAGE = "device_manage"       # 设备管理
    SYSTEM_CONFIG = "system_config"       # 系统配置
    LOG_VIEW = "log_view"                 # 日志查看
    STATISTICS_VIEW = "statistics_view"   # 统计查看


@dataclass
class User:
    """用户信息"""
    user_id: str
    username: str
    email: str
    role: UserRole
    permissions: List[Permission]
    created_time: float
    last_login_time: Optional[float] = None
    is_active: bool = True
    password_hash: Optional[str] = None


@dataclass
class Device:
    """设备信息"""
    device_id: str
    device_name: str
    device_type: str  # android, ios
    status: str       # online, offline, error
    last_seen: float
    user_id: str
    capabilities: Dict[str, Any]
    created_time: float


class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class UserCreateRequest(BaseModel):
    """创建用户请求"""
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    password: str = Field(..., description="密码")
    role: str = Field(..., description="角色")


class WebAdminSystem:
    """
    Web后台管理系统
    
    功能特性：
    - RBAC权限管理
    - 用户认证和授权
    - 数据导入导出管理
    - 设备状态监控
    - 系统统计和分析
    - 日志管理和审计
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化Web管理系统
        
        Args:
            config: 系统配置
        """
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)
        
        # JWT密钥
        self.jwt_secret = self.config.get('jwt_secret', 'your-secret-key')
        self.jwt_algorithm = 'HS256'
        self.jwt_expiration = 24 * 3600  # 24小时
        
        # 权限映射
        self.role_permissions = self._init_role_permissions()
        
        # 初始化数据库
        self._init_database()
        
        # 创建默认管理员
        self._create_default_admin()
        
        # 创建FastAPI应用
        if FASTAPI_AVAILABLE:
            self.app = self._create_fastapi_app()
        else:
            self.app = None
        
        self.logger.info("Web管理系统初始化完成")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'host': '127.0.0.1',
            'port': 8080,
            'debug': True,
            'jwt_secret': 'phone-marking-system-secret-key',
            'database_path': 'web_admin.db',
            'static_dir': './static',
            'templates_dir': './templates',
            'upload_dir': './uploads',
            'export_dir': './exports',
            'log_dir': './logs',
            'default_admin': {
                'username': 'admin',
                'password': 'admin123',
                'email': '<EMAIL>'
            }
        }
    
    def _init_role_permissions(self) -> Dict[UserRole, List[Permission]]:
        """初始化角色权限映射"""
        return {
            UserRole.ADMIN: [
                Permission.USER_MANAGE,
                Permission.DATA_IMPORT,
                Permission.DATA_EXPORT,
                Permission.DEVICE_MANAGE,
                Permission.SYSTEM_CONFIG,
                Permission.LOG_VIEW,
                Permission.STATISTICS_VIEW
            ],
            UserRole.OPERATOR: [
                Permission.DATA_IMPORT,
                Permission.DATA_EXPORT,
                Permission.DEVICE_MANAGE,
                Permission.STATISTICS_VIEW
            ],
            UserRole.VIEWER: [
                Permission.STATISTICS_VIEW,
                Permission.LOG_VIEW
            ]
        }
    
    def _init_database(self):
        """初始化数据库"""
        if not DATABASE_AVAILABLE:
            self.logger.warning("数据库不可用")
            self.db_manager = None
            return
        
        try:
            # 使用SQLite数据库
            db_path = self.config.get('database_path', 'web_admin.db')
            self.db_manager = AdvancedSQLiteManager(db_path)
            
            # 创建表
            self._create_tables()
            
            self.logger.info("数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            self.db_manager = None
    
    def _create_tables(self):
        """创建数据库表"""
        if not self.db_manager:
            return
        
        # 用户表
        create_users_table = """
        CREATE TABLE IF NOT EXISTS users (
            user_id TEXT PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL,
            permissions TEXT NOT NULL,
            created_time REAL NOT NULL,
            last_login_time REAL,
            is_active BOOLEAN DEFAULT 1
        )
        """
        
        # 设备表
        create_devices_table = """
        CREATE TABLE IF NOT EXISTS devices (
            device_id TEXT PRIMARY KEY,
            device_name TEXT NOT NULL,
            device_type TEXT NOT NULL,
            status TEXT NOT NULL,
            last_seen REAL NOT NULL,
            user_id TEXT NOT NULL,
            capabilities TEXT,
            created_time REAL NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        """
        
        # 操作日志表
        create_logs_table = """
        CREATE TABLE IF NOT EXISTS operation_logs (
            log_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            operation TEXT NOT NULL,
            resource TEXT,
            details TEXT,
            ip_address TEXT,
            user_agent TEXT,
            timestamp REAL NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        """
        
        # 系统统计表
        create_stats_table = """
        CREATE TABLE IF NOT EXISTS system_statistics (
            stat_id INTEGER PRIMARY KEY AUTOINCREMENT,
            stat_type TEXT NOT NULL,
            stat_data TEXT NOT NULL,
            timestamp REAL NOT NULL
        )
        """
        
        try:
            conn = self.db_manager._get_connection("users")
            conn.execute(create_users_table)
            conn.execute(create_devices_table)
            conn.execute(create_logs_table)
            conn.execute(create_stats_table)
            conn.commit()
            
        except Exception as e:
            self.logger.error(f"创建数据库表失败: {e}")
    
    def _create_default_admin(self):
        """创建默认管理员账户"""
        if not self.db_manager:
            return
        
        try:
            admin_config = self.config.get('default_admin', {})
            username = admin_config.get('username', 'admin')
            
            # 检查管理员是否已存在
            if self.get_user_by_username(username):
                return
            
            # 创建管理员账户
            admin_user = User(
                user_id='admin_001',
                username=username,
                email=admin_config.get('email', '<EMAIL>'),
                role=UserRole.ADMIN,
                permissions=self.role_permissions[UserRole.ADMIN],
                created_time=time.time(),
                password_hash=self._hash_password(admin_config.get('password', 'admin123'))
            )
            
            self._save_user_to_db(admin_user)
            self.logger.info(f"创建默认管理员账户: {username}")
            
        except Exception as e:
            self.logger.error(f"创建默认管理员失败: {e}")
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        return self._hash_password(password) == password_hash
    
    def _generate_jwt_token(self, user: User) -> str:
        """生成JWT令牌"""
        if not JWT_AVAILABLE:
            # 简单的token生成
            return f"{user.user_id}:{int(time.time())}"
        
        payload = {
            'user_id': user.user_id,
            'username': user.username,
            'role': user.role.value,
            'exp': datetime.utcnow() + timedelta(seconds=self.jwt_expiration)
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    def _verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证JWT令牌"""
        if not JWT_AVAILABLE:
            # 简单的token验证
            try:
                user_id, timestamp = token.split(':')
                if time.time() - int(timestamp) < self.jwt_expiration:
                    return {'user_id': user_id}
            except:
                pass
            return None
        
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

    def _save_user_to_db(self, user: User):
        """保存用户到数据库"""
        if not self.db_manager:
            return

        try:
            conn = self.db_manager._get_connection("users")

            sql = """
            REPLACE INTO users (
                user_id, username, email, password_hash, role, permissions,
                created_time, last_login_time, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            permissions_json = json.dumps([p.value for p in user.permissions])

            conn.execute(sql, (
                user.user_id, user.username, user.email, user.password_hash,
                user.role.value, permissions_json, user.created_time,
                user.last_login_time, user.is_active
            ))
            conn.commit()

        except Exception as e:
            self.logger.error(f"保存用户到数据库失败: {e}")

    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        if not self.db_manager:
            return None

        try:
            conn = self.db_manager._get_connection("users")
            cursor = conn.execute(
                "SELECT * FROM users WHERE username = ? AND is_active = 1",
                (username,)
            )
            row = cursor.fetchone()

            if not row:
                return None

            permissions = [Permission(p) for p in json.loads(row[5])]

            return User(
                user_id=row[0],
                username=row[1],
                email=row[2],
                role=UserRole(row[4]),
                permissions=permissions,
                created_time=row[6],
                last_login_time=row[7],
                is_active=bool(row[8]),
                password_hash=row[3]
            )

        except Exception as e:
            self.logger.error(f"获取用户失败: {e}")
            return None

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """根据用户ID获取用户"""
        if not self.db_manager:
            return None

        try:
            conn = self.db_manager._get_connection("users")
            cursor = conn.execute(
                "SELECT * FROM users WHERE user_id = ? AND is_active = 1",
                (user_id,)
            )
            row = cursor.fetchone()

            if not row:
                return None

            permissions = [Permission(p) for p in json.loads(row[5])]

            return User(
                user_id=row[0],
                username=row[1],
                email=row[2],
                role=UserRole(row[4]),
                permissions=permissions,
                created_time=row[6],
                last_login_time=row[7],
                is_active=bool(row[8]),
                password_hash=row[3]
            )

        except Exception as e:
            self.logger.error(f"获取用户失败: {e}")
            return None

    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        user = self.get_user_by_username(username)
        if not user:
            return None

        if not self._verify_password(password, user.password_hash):
            return None

        # 更新最后登录时间
        user.last_login_time = time.time()
        self._save_user_to_db(user)

        return user

    def create_user(self, username: str, email: str, password: str, role: str) -> str:
        """创建用户"""
        # 检查用户名是否已存在
        if self.get_user_by_username(username):
            raise ValueError("用户名已存在")

        # 验证角色
        try:
            user_role = UserRole(role)
        except ValueError:
            raise ValueError("无效的用户角色")

        # 生成用户ID
        user_id = f"user_{int(time.time())}_{hash(username) % 10000}"

        # 创建用户
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            role=user_role,
            permissions=self.role_permissions[user_role],
            created_time=time.time(),
            password_hash=self._hash_password(password)
        )

        self._save_user_to_db(user)
        self.logger.info(f"创建用户: {username}")

        return user_id

    def check_permission(self, user: User, permission: Permission) -> bool:
        """检查用户权限"""
        return permission in user.permissions

    def log_operation(self, user_id: str, operation: str, resource: str = None,
                     details: str = None, ip_address: str = None, user_agent: str = None):
        """记录操作日志"""
        if not self.db_manager:
            return

        try:
            conn = self.db_manager._get_connection("operation_logs")

            sql = """
            INSERT INTO operation_logs (
                user_id, operation, resource, details, ip_address, user_agent, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            conn.execute(sql, (
                user_id, operation, resource, details, ip_address, user_agent, time.time()
            ))
            conn.commit()

        except Exception as e:
            self.logger.error(f"记录操作日志失败: {e}")

    def get_operation_logs(self, user_id: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取操作日志"""
        if not self.db_manager:
            return []

        try:
            conn = self.db_manager._get_connection("operation_logs")

            if user_id:
                cursor = conn.execute(
                    "SELECT * FROM operation_logs WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?",
                    (user_id, limit)
                )
            else:
                cursor = conn.execute(
                    "SELECT * FROM operation_logs ORDER BY timestamp DESC LIMIT ?",
                    (limit,)
                )

            logs = []
            for row in cursor.fetchall():
                logs.append({
                    'log_id': row[0],
                    'user_id': row[1],
                    'operation': row[2],
                    'resource': row[3],
                    'details': row[4],
                    'ip_address': row[5],
                    'user_agent': row[6],
                    'timestamp': row[7],
                    'formatted_time': datetime.fromtimestamp(row[7]).strftime('%Y-%m-%d %H:%M:%S')
                })

            return logs

        except Exception as e:
            self.logger.error(f"获取操作日志失败: {e}")
            return []

    def register_device(self, device_name: str, device_type: str, user_id: str,
                       capabilities: Dict[str, Any] = None) -> str:
        """注册设备"""
        device_id = f"device_{int(time.time())}_{hash(device_name) % 10000}"

        device = Device(
            device_id=device_id,
            device_name=device_name,
            device_type=device_type,
            status="offline",
            last_seen=time.time(),
            user_id=user_id,
            capabilities=capabilities or {},
            created_time=time.time()
        )

        self._save_device_to_db(device)
        self.logger.info(f"注册设备: {device_name}")

        return device_id

    def _save_device_to_db(self, device: Device):
        """保存设备到数据库"""
        if not self.db_manager:
            return

        try:
            conn = self.db_manager._get_connection("devices")

            sql = """
            REPLACE INTO devices (
                device_id, device_name, device_type, status, last_seen,
                user_id, capabilities, created_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            capabilities_json = json.dumps(device.capabilities)

            conn.execute(sql, (
                device.device_id, device.device_name, device.device_type,
                device.status, device.last_seen, device.user_id,
                capabilities_json, device.created_time
            ))
            conn.commit()

        except Exception as e:
            self.logger.error(f"保存设备到数据库失败: {e}")

    def get_devices(self, user_id: str = None) -> List[Device]:
        """获取设备列表"""
        if not self.db_manager:
            return []

        try:
            conn = self.db_manager._get_connection("devices")

            if user_id:
                cursor = conn.execute(
                    "SELECT * FROM devices WHERE user_id = ? ORDER BY created_time DESC",
                    (user_id,)
                )
            else:
                cursor = conn.execute(
                    "SELECT * FROM devices ORDER BY created_time DESC"
                )

            devices = []
            for row in cursor.fetchall():
                capabilities = json.loads(row[6]) if row[6] else {}

                devices.append(Device(
                    device_id=row[0],
                    device_name=row[1],
                    device_type=row[2],
                    status=row[3],
                    last_seen=row[4],
                    user_id=row[5],
                    capabilities=capabilities,
                    created_time=row[7]
                ))

            return devices

        except Exception as e:
            self.logger.error(f"获取设备列表失败: {e}")
            return []

    def update_device_status(self, device_id: str, status: str):
        """更新设备状态"""
        if not self.db_manager:
            return

        try:
            conn = self.db_manager._get_connection("devices")

            conn.execute(
                "UPDATE devices SET status = ?, last_seen = ? WHERE device_id = ?",
                (status, time.time(), device_id)
            )
            conn.commit()

        except Exception as e:
            self.logger.error(f"更新设备状态失败: {e}")

    def check_device_connection(self, device_id: str) -> bool:
        """检查设备连接状态"""
        devices = self.get_devices()
        for device in devices:
            if device.device_id == device_id:
                # 如果设备在5分钟内有活动，认为是在线的
                return device.status == "online" and (time.time() - device.last_seen) < 300
        return False

    def _create_fastapi_app(self) -> FastAPI:
        """创建FastAPI应用"""
        app = FastAPI(
            title="电话号码标记识别管理系统",
            description="Web后台管理系统",
            version="1.0.0"
        )

        # 配置CORS
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # 静态文件
        static_dir = self.config.get('static_dir', './static')
        if os.path.exists(static_dir):
            app.mount("/static", StaticFiles(directory=static_dir), name="static")

        # 模板
        templates_dir = self.config.get('templates_dir', './templates')
        if os.path.exists(templates_dir):
            templates = Jinja2Templates(directory=templates_dir)
        else:
            templates = None

        # 认证依赖
        security = HTTPBearer()

        async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
            """获取当前用户"""
            token = credentials.credentials
            payload = self._verify_jwt_token(token)

            if not payload:
                raise HTTPException(status_code=401, detail="无效的认证令牌")

            user = self.get_user_by_id(payload['user_id'])
            if not user:
                raise HTTPException(status_code=401, detail="用户不存在")

            return user

        def require_permission(permission: Permission):
            """权限检查装饰器"""
            def permission_checker(user: User = Depends(get_current_user)) -> User:
                if not self.check_permission(user, permission):
                    raise HTTPException(status_code=403, detail="权限不足")
                return user
            return permission_checker

        # 认证接口
        @app.post("/api/auth/login")
        async def login(request: LoginRequest):
            """用户登录"""
            user = self.authenticate_user(request.username, request.password)
            if not user:
                raise HTTPException(status_code=401, detail="用户名或密码错误")

            token = self._generate_jwt_token(user)

            return {
                "status": "success",
                "token": token,
                "user": {
                    "user_id": user.user_id,
                    "username": user.username,
                    "email": user.email,
                    "role": user.role.value,
                    "permissions": [p.value for p in user.permissions]
                }
            }

        @app.post("/api/auth/logout")
        async def logout(user: User = Depends(get_current_user)):
            """用户登出"""
            # 这里可以实现token黑名单等逻辑
            return {"status": "success", "message": "登出成功"}

        # 用户管理接口
        @app.post("/api/users")
        async def create_user_api(
            request: UserCreateRequest,
            user: User = Depends(require_permission(Permission.USER_MANAGE))
        ):
            """创建用户"""
            try:
                user_id = self.create_user(
                    request.username,
                    request.email,
                    request.password,
                    request.role
                )

                self.log_operation(
                    user.user_id,
                    "create_user",
                    f"user:{user_id}",
                    f"创建用户: {request.username}"
                )

                return {"status": "success", "user_id": user_id}

            except ValueError as e:
                raise HTTPException(status_code=400, detail=str(e))

        @app.get("/api/users")
        async def list_users(user: User = Depends(require_permission(Permission.USER_MANAGE))):
            """获取用户列表"""
            # 这里需要实现获取所有用户的方法
            return {"status": "success", "users": []}

        # 设备管理接口
        @app.get("/api/devices")
        async def list_devices(user: User = Depends(require_permission(Permission.DEVICE_MANAGE))):
            """获取设备列表"""
            devices = self.get_devices()

            return {
                "status": "success",
                "devices": [asdict(device) for device in devices]
            }

        @app.post("/api/devices/{device_id}/status")
        async def update_device_status_api(
            device_id: str,
            status: str,
            user: User = Depends(require_permission(Permission.DEVICE_MANAGE))
        ):
            """更新设备状态"""
            self.update_device_status(device_id, status)

            self.log_operation(
                user.user_id,
                "update_device_status",
                f"device:{device_id}",
                f"更新设备状态: {status}"
            )

            return {"status": "success"}

        @app.get("/api/devices/{device_id}/check")
        async def check_device_connection_api(
            device_id: str,
            user: User = Depends(get_current_user)
        ):
            """检查设备连接状态"""
            is_connected = self.check_device_connection(device_id)

            return {
                "status": "success",
                "device_id": device_id,
                "is_connected": is_connected
            }

        # 日志接口
        @app.get("/api/logs")
        async def get_logs(
            limit: int = 100,
            user: User = Depends(require_permission(Permission.LOG_VIEW))
        ):
            """获取操作日志"""
            logs = self.get_operation_logs(limit=limit)

            return {
                "status": "success",
                "logs": logs
            }

        # 主页
        @app.get("/", response_class=HTMLResponse)
        async def index(request: Request):
            """主页"""
            if templates:
                return templates.TemplateResponse("index.html", {"request": request})
            else:
                return HTMLResponse("""
                <html>
                    <head><title>电话号码标记识别管理系统</title></head>
                    <body>
                        <h1>电话号码标记识别管理系统</h1>
                        <p>Web后台管理系统正在运行</p>
                        <p>API文档: <a href="/docs">/docs</a></p>
                    </body>
                </html>
                """)

        return app

    def run(self, host: Optional[str] = None, port: Optional[int] = None):
        """运行Web管理系统"""
        if not FASTAPI_AVAILABLE:
            self.logger.error("FastAPI不可用，无法启动Web管理系统")
            return

        host = host or self.config.get('host', '127.0.0.1')
        port = port or self.config.get('port', 8080)

        self.logger.info(f"启动Web管理系统: http://{host}:{port}")

        try:
            uvicorn.run(
                self.app,
                host=host,
                port=port,
                log_level="info"
            )
        except Exception as e:
            self.logger.error(f"Web管理系统启动失败: {e}")


def create_web_admin_system(config: Optional[Dict[str, Any]] = None) -> WebAdminSystem:
    """创建Web管理系统实例"""
    return WebAdminSystem(config)


def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    system = create_web_admin_system()
    system.run()


if __name__ == '__main__':
    main()
