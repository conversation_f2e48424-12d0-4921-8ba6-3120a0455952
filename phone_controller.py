#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Controller - Python + ADB 电话控制工具
支持批量拨号、查询标记、数据导出等功能
"""

import subprocess
import time
import json
import csv
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional
import os

class PhoneController:
    def __init__(self):
        self.device_connected = self._check_device()
        
    def _check_device(self) -> bool:
        """检查设备连接状态"""
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            devices = result.stdout.strip().split('\n')[1:]
            connected_devices = [d for d in devices if d.strip() and 'device' in d]
            return len(connected_devices) > 0
        except Exception as e:
            print(f"ADB检查失败: {e}")
            return False
    
    def make_call(self, phone_number: str, duration: int = 5) -> bool:
        """
        拨打电话
        :param phone_number: 电话号码
        :param duration: 通话时长(秒)
        :return: 是否成功
        """
        if not self.device_connected:
            print("设备未连接")
            return False
            
        try:
            print(f"正在拨打: {phone_number}")
            
            # 拨打电话
            subprocess.run(['adb', 'shell', 'am', 'start', '-a', 'android.intent.action.CALL', 
                          '-d', f'tel:{phone_number}'])
            time.sleep(3)
            
            # 等待通话建立
            time.sleep(duration)
            
            # 挂断电话
            subprocess.run(['adb', 'shell', 'input', 'keyevent', 'KEYCODE_ENDCALL'])
            time.sleep(2)
            
            print(f"通话完成: {phone_number}")
            return True
            
        except Exception as e:
            print(f"拨号失败: {e}")
            return False
    
    def batch_call(self, phone_list: List[str], interval: int = 3) -> Dict[str, bool]:
        """
        批量拨打电话
        :param phone_list: 电话号码列表
        :param interval: 拨号间隔(秒)
        :return: 拨号结果字典
        """
        results = {}
        
        for i, phone in enumerate(phone_list, 1):
            print(f"进度: {i}/{len(phone_list)}")
            results[phone] = self.make_call(phone)
            
            if i < len(phone_list):
                print(f"等待 {interval} 秒...")
                time.sleep(interval)
        
        return results
    
    def get_call_log(self) -> List[Dict]:
        """获取通话记录"""
        try:
            result = subprocess.run(['adb', 'shell', 'content', 'query', '--uri', 'content://call_log/calls'], 
                                  capture_output=True, text=True)
            
            # 解析通话记录
            lines = result.stdout.strip().split('\n')
            call_logs = []
            
            for line in lines:
                if 'Row:' in line:
                    # 解析通话记录行
                    parts = line.split(',')
                    call_log = {}
                    for part in parts:
                        if '=' in part:
                            key, value = part.split('=', 1)
                            call_log[key.strip()] = value.strip()
                    call_logs.append(call_log)
            
            return call_logs
            
        except Exception as e:
            print(f"获取通话记录失败: {e}")
            return []
    
    def take_screenshot(self, filename: str = None) -> str:
        """截图"""
        if not filename:
            filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        try:
            subprocess.run(['adb', 'shell', 'screencap', '/sdcard/screenshot.png'])
            subprocess.run(['adb', 'pull', '/sdcard/screenshot.png', filename])
            return filename
        except Exception as e:
            print(f"截图失败: {e}")
            return ""
    
    def export_results(self, results: Dict[str, bool], filename: str = None) -> str:
        """
        导出结果到CSV/Excel
        :param results: 拨号结果字典
        :param filename: 文件名
        :return: 导出文件路径
        """
        if not filename:
            filename = f"call_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # 准备数据
        data = []
        for phone, success in results.items():
            data.append({
                'phone_number': phone,
                'call_success': success,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # 导出到Excel
        df = pd.DataFrame(data)
        df.to_excel(filename, index=False)
        
        print(f"结果已导出到: {filename}")
        return filename
    
    def query_phone_mark(self, phone_number: str) -> Dict:
        """
        查询电话号码标记
        这里可以集成第三方API或网页爬虫
        """
        # 示例实现 - 实际使用时需要替换为真实的API
        return {
            'phone_number': phone_number,
            'mark_type': '未知',
            'mark_count': 0,
            'source': '本地查询'
        }

def main():
    """主函数 - 演示使用"""
    controller = PhoneController()
    
    if not controller.device_connected:
        print("请连接Android设备并开启USB调试")
        return
    
    # 示例电话号码列表
    test_phones = [
        "10086",
        "10000", 
        "10010"
    ]
    
    print("开始批量拨号测试...")
    results = controller.batch_call(test_phones, interval=5)
    
    # 导出结果
    controller.export_results(results)
    
    print("测试完成!")

if __name__ == "__main__":
    main() 