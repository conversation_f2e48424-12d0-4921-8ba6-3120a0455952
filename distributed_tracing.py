"""
分布式追踪系统
轻量级的分布式追踪实现，用于跟踪跨服务的请求链路
采用保守的实施策略，无外部依赖，易于集成

设计原则：
1. 轻量级实现：无外部依赖，性能开销最小
2. 易于集成：简单的API，最小化代码侵入
3. 向后兼容：可选启用，不影响现有功能
4. 数据安全：本地存储，不涉及外部服务
"""

import time
import uuid
import threading
import json
import logging
from typing import Dict, List, Any, Optional, ContextManager
from datetime import datetime, timedelta
from contextlib import contextmanager
from dataclasses import dataclass, asdict
from collections import defaultdict, deque


@dataclass
class Span:
    """追踪跨度"""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str]
    operation_name: str
    service_name: str
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    status: str = "ok"  # ok, error
    tags: Dict[str, Any] = None
    logs: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}
        if self.logs is None:
            self.logs = []
    
    def finish(self, status: str = "ok"):
        """结束跨度"""
        self.end_time = time.time()
        self.duration_ms = (self.end_time - self.start_time) * 1000
        self.status = status
    
    def add_tag(self, key: str, value: Any):
        """添加标签"""
        self.tags[key] = value
    
    def add_log(self, message: str, level: str = "info", **kwargs):
        """添加日志"""
        log_entry = {
            'timestamp': time.time(),
            'message': message,
            'level': level,
            **kwargs
        }
        self.logs.append(log_entry)


@dataclass
class Trace:
    """追踪链路"""
    trace_id: str
    spans: List[Span]
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    service_count: int = 0
    span_count: int = 0
    error_count: int = 0
    
    def finish(self):
        """结束追踪"""
        if self.spans:
            self.end_time = max(span.end_time or span.start_time for span in self.spans)
            self.start_time = min(span.start_time for span in self.spans)
            self.duration_ms = (self.end_time - self.start_time) * 1000
            
            # 统计信息
            self.span_count = len(self.spans)
            self.service_count = len(set(span.service_name for span in self.spans))
            self.error_count = sum(1 for span in self.spans if span.status == "error")


class TracingContext:
    """追踪上下文"""
    
    def __init__(self):
        self._local = threading.local()
    
    def get_current_span(self) -> Optional[Span]:
        """获取当前跨度"""
        return getattr(self._local, 'current_span', None)
    
    def set_current_span(self, span: Optional[Span]):
        """设置当前跨度"""
        self._local.current_span = span
    
    def get_trace_id(self) -> Optional[str]:
        """获取当前追踪ID"""
        current_span = self.get_current_span()
        return current_span.trace_id if current_span else None


class DistributedTracer:
    """
    分布式追踪器
    
    功能特性：
    - 轻量级实现，无外部依赖
    - 线程安全的上下文管理
    - 自动垃圾回收，防止内存泄漏
    - 详细的性能统计
    - 可配置的采样率
    """
    
    def __init__(self, service_name: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化分布式追踪器
        
        Args:
            service_name: 服务名称
            config: 配置参数
        """
        self.service_name = service_name
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)
        
        # 追踪数据存储
        self.traces: Dict[str, Trace] = {}
        self.spans: Dict[str, Span] = {}
        
        # 上下文管理
        self.context = TracingContext()
        
        # 性能统计
        self.stats = {
            'total_traces': 0,
            'total_spans': 0,
            'active_traces': 0,
            'active_spans': 0,
            'error_traces': 0,
            'avg_trace_duration': 0.0,
            'sampling_rate': self.config.get('sampling_rate', 1.0)
        }
        
        # 线程安全
        self._lock = threading.Lock()
        
        # 自动清理
        self._cleanup_interval = self.config.get('cleanup_interval', 300)  # 5分钟
        self._max_traces = self.config.get('max_traces', 1000)
        self._last_cleanup = time.time()
        
        self.logger.info(f"分布式追踪器初始化完成: {service_name}")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'enabled': True,
            'sampling_rate': 1.0,  # 100%采样
            'max_traces': 1000,    # 最大追踪数量
            'max_spans_per_trace': 100,  # 每个追踪最大跨度数
            'cleanup_interval': 300,     # 清理间隔（秒）
            'trace_timeout': 3600,       # 追踪超时（秒）
            'enable_logs': True,         # 启用日志记录
            'log_level': 'INFO'
        }
    
    def start_trace(self, operation_name: str, trace_id: Optional[str] = None) -> str:
        """
        开始新的追踪
        
        Args:
            operation_name: 操作名称
            trace_id: 可选的追踪ID（用于跨服务传递）
            
        Returns:
            追踪ID
        """
        if not self.config.get('enabled', True):
            return ""
        
        # 采样检查
        if not self._should_sample():
            return ""
        
        trace_id = trace_id or self._generate_trace_id()
        
        with self._lock:
            if trace_id not in self.traces:
                trace = Trace(
                    trace_id=trace_id,
                    spans=[],
                    start_time=time.time()
                )
                self.traces[trace_id] = trace
                self.stats['total_traces'] += 1
                self.stats['active_traces'] += 1
        
        # 创建根跨度
        root_span = self.start_span(operation_name, trace_id=trace_id)
        
        self.logger.debug(f"开始追踪: {trace_id}, 操作: {operation_name}")
        return trace_id
    
    def start_span(self, operation_name: str, trace_id: Optional[str] = None, 
                   parent_span_id: Optional[str] = None) -> Optional[Span]:
        """
        开始新的跨度
        
        Args:
            operation_name: 操作名称
            trace_id: 追踪ID
            parent_span_id: 父跨度ID
            
        Returns:
            跨度对象
        """
        if not self.config.get('enabled', True):
            return None
        
        # 获取当前上下文
        current_span = self.context.get_current_span()
        if not trace_id and current_span:
            trace_id = current_span.trace_id
        if not parent_span_id and current_span:
            parent_span_id = current_span.span_id
        
        if not trace_id:
            return None
        
        span_id = self._generate_span_id()
        span = Span(
            trace_id=trace_id,
            span_id=span_id,
            parent_span_id=parent_span_id,
            operation_name=operation_name,
            service_name=self.service_name,
            start_time=time.time()
        )
        
        with self._lock:
            self.spans[span_id] = span
            if trace_id in self.traces:
                self.traces[trace_id].spans.append(span)
            self.stats['total_spans'] += 1
            self.stats['active_spans'] += 1
        
        # 设置为当前跨度
        self.context.set_current_span(span)
        
        self.logger.debug(f"开始跨度: {span_id}, 操作: {operation_name}")
        return span
    
    def finish_span(self, span: Optional[Span] = None, status: str = "ok"):
        """
        结束跨度
        
        Args:
            span: 跨度对象，None表示当前跨度
            status: 状态（ok, error）
        """
        if not self.config.get('enabled', True):
            return
        
        if span is None:
            span = self.context.get_current_span()
        
        if span is None:
            return
        
        span.finish(status)
        
        with self._lock:
            self.stats['active_spans'] -= 1
            if status == "error":
                # 标记追踪为错误
                if span.trace_id in self.traces:
                    trace = self.traces[span.trace_id]
                    if trace.error_count == 0:  # 第一个错误
                        self.stats['error_traces'] += 1
        
        # 清除当前跨度
        if self.context.get_current_span() == span:
            self.context.set_current_span(None)
        
        self.logger.debug(f"结束跨度: {span.span_id}, 状态: {status}, 耗时: {span.duration_ms:.2f}ms")
    
    def finish_trace(self, trace_id: str):
        """
        结束追踪
        
        Args:
            trace_id: 追踪ID
        """
        if not self.config.get('enabled', True):
            return
        
        with self._lock:
            if trace_id in self.traces:
                trace = self.traces[trace_id]
                trace.finish()
                self.stats['active_traces'] -= 1
                
                # 更新平均追踪时间
                if self.stats['total_traces'] > 0:
                    current_avg = self.stats['avg_trace_duration']
                    new_duration = trace.duration_ms or 0
                    self.stats['avg_trace_duration'] = (
                        (current_avg * (self.stats['total_traces'] - 1) + new_duration) / 
                        self.stats['total_traces']
                    )
        
        self.logger.debug(f"结束追踪: {trace_id}")
        
        # 定期清理
        self._maybe_cleanup()
    
    @contextmanager
    def trace(self, operation_name: str, trace_id: Optional[str] = None) -> ContextManager[Optional[Span]]:
        """
        追踪上下文管理器
        
        Args:
            operation_name: 操作名称
            trace_id: 可选的追踪ID
            
        Yields:
            跨度对象
        """
        if not trace_id:
            trace_id = self.start_trace(operation_name)
        
        span = self.start_span(operation_name, trace_id)
        
        try:
            yield span
            self.finish_span(span, "ok")
        except Exception as e:
            if span:
                span.add_log(f"异常: {str(e)}", "error")
            self.finish_span(span, "error")
            raise
        finally:
            if trace_id and span and span.parent_span_id is None:
                # 根跨度，结束追踪
                self.finish_trace(trace_id)
    
    @contextmanager
    def span(self, operation_name: str) -> ContextManager[Optional[Span]]:
        """
        跨度上下文管理器
        
        Args:
            operation_name: 操作名称
            
        Yields:
            跨度对象
        """
        span = self.start_span(operation_name)
        
        try:
            yield span
            self.finish_span(span, "ok")
        except Exception as e:
            if span:
                span.add_log(f"异常: {str(e)}", "error")
            self.finish_span(span, "error")
            raise
    
    def get_trace(self, trace_id: str) -> Optional[Trace]:
        """
        获取追踪信息
        
        Args:
            trace_id: 追踪ID
            
        Returns:
            追踪对象
        """
        return self.traces.get(trace_id)
    
    def get_span(self, span_id: str) -> Optional[Span]:
        """
        获取跨度信息
        
        Args:
            span_id: 跨度ID
            
        Returns:
            跨度对象
        """
        return self.spans.get(span_id)
    
    def get_active_traces(self) -> List[Trace]:
        """获取活跃的追踪"""
        with self._lock:
            return [trace for trace in self.traces.values() if trace.end_time is None]
    
    def get_trace_statistics(self) -> Dict[str, Any]:
        """获取追踪统计信息"""
        with self._lock:
            stats = self.stats.copy()
            
            # 计算额外统计信息
            if self.traces:
                completed_traces = [t for t in self.traces.values() if t.end_time is not None]
                if completed_traces:
                    durations = [t.duration_ms for t in completed_traces if t.duration_ms]
                    if durations:
                        stats['min_trace_duration'] = min(durations)
                        stats['max_trace_duration'] = max(durations)
                        stats['median_trace_duration'] = sorted(durations)[len(durations)//2]
                
                # 服务调用统计
                service_calls = defaultdict(int)
                for trace in self.traces.values():
                    for span in trace.spans:
                        service_calls[span.service_name] += 1
                stats['service_call_distribution'] = dict(service_calls)
            
            return stats
    
    def export_traces(self, trace_ids: Optional[List[str]] = None, 
                     format: str = "json") -> str:
        """
        导出追踪数据
        
        Args:
            trace_ids: 要导出的追踪ID列表，None表示导出所有
            format: 导出格式（json）
            
        Returns:
            导出的数据字符串
        """
        with self._lock:
            if trace_ids is None:
                traces_to_export = list(self.traces.values())
            else:
                traces_to_export = [self.traces[tid] for tid in trace_ids if tid in self.traces]
        
        if format == "json":
            export_data = {
                'service_name': self.service_name,
                'export_time': datetime.now().isoformat(),
                'traces': [self._trace_to_dict(trace) for trace in traces_to_export]
            }
            return json.dumps(export_data, indent=2, default=str)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def _trace_to_dict(self, trace: Trace) -> Dict[str, Any]:
        """将追踪转换为字典"""
        return {
            'trace_id': trace.trace_id,
            'start_time': trace.start_time,
            'end_time': trace.end_time,
            'duration_ms': trace.duration_ms,
            'service_count': trace.service_count,
            'span_count': trace.span_count,
            'error_count': trace.error_count,
            'spans': [asdict(span) for span in trace.spans]
        }
    
    def _should_sample(self) -> bool:
        """检查是否应该采样"""
        import random
        return random.random() < self.config.get('sampling_rate', 1.0)
    
    def _generate_trace_id(self) -> str:
        """生成追踪ID"""
        return str(uuid.uuid4())
    
    def _generate_span_id(self) -> str:
        """生成跨度ID"""
        return str(uuid.uuid4())
    
    def _maybe_cleanup(self):
        """可能执行清理"""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            self._cleanup_old_traces()
            self._last_cleanup = current_time
    
    def _cleanup_old_traces(self):
        """清理旧的追踪数据"""
        current_time = time.time()
        timeout = self.config.get('trace_timeout', 3600)
        
        with self._lock:
            # 清理超时的追踪
            expired_traces = []
            for trace_id, trace in self.traces.items():
                if current_time - trace.start_time > timeout:
                    expired_traces.append(trace_id)
            
            for trace_id in expired_traces:
                trace = self.traces.pop(trace_id, None)
                if trace:
                    # 清理相关的跨度
                    for span in trace.spans:
                        self.spans.pop(span.span_id, None)
            
            # 如果追踪数量过多，清理最旧的
            if len(self.traces) > self._max_traces:
                sorted_traces = sorted(
                    self.traces.items(), 
                    key=lambda x: x[1].start_time
                )
                
                to_remove = len(self.traces) - self._max_traces
                for trace_id, trace in sorted_traces[:to_remove]:
                    self.traces.pop(trace_id, None)
                    for span in trace.spans:
                        self.spans.pop(span.span_id, None)
            
            if expired_traces:
                self.logger.info(f"清理了 {len(expired_traces)} 个过期追踪")


# 全局追踪器实例
_global_tracers: Dict[str, DistributedTracer] = {}
_tracer_lock = threading.Lock()


def get_tracer(service_name: str, config: Optional[Dict[str, Any]] = None) -> DistributedTracer:
    """
    获取或创建追踪器实例
    
    Args:
        service_name: 服务名称
        config: 配置参数
        
    Returns:
        追踪器实例
    """
    with _tracer_lock:
        if service_name not in _global_tracers:
            _global_tracers[service_name] = DistributedTracer(service_name, config)
        return _global_tracers[service_name]


def create_tracer(service_name: str, config: Optional[Dict[str, Any]] = None) -> DistributedTracer:
    """
    创建新的追踪器实例
    
    Args:
        service_name: 服务名称
        config: 配置参数
        
    Returns:
        追踪器实例
    """
    return DistributedTracer(service_name, config)


# 便捷函数
def trace(operation_name: str, service_name: str = "default"):
    """追踪装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            tracer = get_tracer(service_name)
            with tracer.trace(operation_name) as span:
                if span:
                    span.add_tag("function", func.__name__)
                    span.add_tag("args_count", len(args))
                    span.add_tag("kwargs_count", len(kwargs))
                return func(*args, **kwargs)
        return wrapper
    return decorator


def test_distributed_tracing():
    """测试分布式追踪"""
    print("=== 测试分布式追踪 ===")
    
    # 创建追踪器
    tracer = create_tracer("test-service", {
        'sampling_rate': 1.0,
        'max_traces': 100
    })
    
    print("1. 基础追踪测试")
    
    # 测试基础追踪
    with tracer.trace("test_operation") as span:
        if span:
            span.add_tag("test_type", "basic")
            span.add_log("开始测试操作")
        
        time.sleep(0.01)  # 模拟操作
        
        # 嵌套跨度
        with tracer.span("nested_operation") as nested_span:
            if nested_span:
                nested_span.add_tag("nested", True)
                nested_span.add_log("嵌套操作")
            time.sleep(0.005)
    
    print("2. 统计信息测试")
    stats = tracer.get_trace_statistics()
    print(f"  总追踪数: {stats['total_traces']}")
    print(f"  总跨度数: {stats['total_spans']}")
    print(f"  平均追踪时间: {stats['avg_trace_duration']:.2f}ms")
    
    print("3. 数据导出测试")
    export_data = tracer.export_traces()
    print(f"  导出数据长度: {len(export_data)} 字符")
    
    print("✓ 分布式追踪测试完成")


if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_distributed_tracing()
