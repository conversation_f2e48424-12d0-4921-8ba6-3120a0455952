"""
数据库升级脚本
为dial_records表添加新的归属地相关字段
"""

import sqlite3
import logging

def upgrade_database(db_path='phone_marks.db'):
    """升级数据库结构"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(dial_records)")
        columns = [column[1] for column in cursor.fetchall()]
        logger.info(f"当前表字段: {columns}")
        
        # 需要添加的新字段
        new_columns = {
            'isp': 'TEXT',  # 运营商
            'location_source': 'TEXT'  # 归属地数据源
        }
        
        # 添加缺失的字段
        for column_name, column_type in new_columns.items():
            if column_name not in columns:
                try:
                    cursor.execute(f'ALTER TABLE dial_records ADD COLUMN {column_name} {column_type}')
                    logger.info(f"成功添加字段: {column_name}")
                except Exception as e:
                    logger.error(f"添加字段 {column_name} 失败: {e}")
            else:
                logger.info(f"字段 {column_name} 已存在")
        
        # 提交更改
        conn.commit()
        
        # 验证表结构
        cursor.execute("PRAGMA table_info(dial_records)")
        updated_columns = [column[1] for column in cursor.fetchall()]
        logger.info(f"更新后表字段: {updated_columns}")
        
        conn.close()
        logger.info("数据库升级完成")
        
    except Exception as e:
        logger.error(f"数据库升级失败: {e}")
        raise

if __name__ == '__main__':
    upgrade_database()
