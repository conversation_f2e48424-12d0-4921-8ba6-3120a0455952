"""
高级SQLite管理器 - 分片优化版本
采用保守的分片策略，在保持向后兼容性的前提下提升数据库性能
支持时间分片、功能分片和智能查询路由

设计原则：
1. 向后兼容：保持原有API不变
2. 渐进升级：可选择性启用分片功能
3. 故障隔离：单个分片故障不影响整体系统
4. 性能监控：详细的性能统计和监控
"""

import sqlite3
import threading
import logging
import time
import os
import json
from typing import Dict, List, Any, Optional, Union
from contextlib import contextmanager
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum


class ShardingStrategy(Enum):
    """分片策略枚举"""
    NONE = "none"           # 不分片（默认模式）
    TIME_BASED = "time"     # 按时间分片
    FUNCTIONAL = "functional"  # 按功能分片
    HYBRID = "hybrid"       # 混合分片


@dataclass
class ShardConfig:
    """分片配置"""
    strategy: ShardingStrategy = ShardingStrategy.NONE
    time_unit: str = "year"  # year, month, day
    max_records_per_shard: int = 100000  # 每个分片最大记录数
    auto_create_shards: bool = True  # 自动创建新分片
    enable_cross_shard_queries: bool = True  # 启用跨分片查询


class AdvancedSQLiteManager:
    """
    高级SQLite管理器
    
    功能特性：
    - 智能分片管理：支持时间和功能分片
    - 查询路由：自动路由查询到正确的分片
    - 性能优化：连接池、缓存、索引优化
    - 监控统计：详细的性能指标收集
    - 向后兼容：完全兼容原有DatabaseManager接口
    """
    
    def __init__(self, base_db_path: str = "phone_marks.db", shard_config: Optional[ShardConfig] = None):
        """
        初始化高级SQLite管理器
        
        Args:
            base_db_path: 基础数据库文件路径
            shard_config: 分片配置，None表示使用默认配置
        """
        self.base_db_path = base_db_path
        self.shard_config = shard_config or ShardConfig()
        self.logger = logging.getLogger(__name__)
        
        # 线程安全
        self._local = threading.local()
        self._lock = threading.Lock()
        
        # 分片管理
        self.shards = {}  # 分片名称 -> 数据库路径映射
        self.shard_connections = {}  # 分片连接缓存
        
        # 性能统计
        self.stats = {
            'total_queries': 0,
            'total_time': 0.0,
            'slow_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'shard_queries': {},  # 每个分片的查询统计
            'cross_shard_queries': 0
        }
        
        # 查询缓存
        self._query_cache = {}
        self._cache_max_size = 1000
        
        # 初始化
        self._init_sharding_system()
        self.logger.info(f"高级SQLite管理器初始化完成，分片策略: {self.shard_config.strategy.value}")
    
    def _init_sharding_system(self):
        """
        初始化分片系统
        
        采用保守策略：
        1. 首先检查是否启用分片
        2. 如果未启用，使用传统单数据库模式
        3. 如果启用，逐步创建和配置分片
        """
        try:
            if self.shard_config.strategy == ShardingStrategy.NONE:
                # 传统模式：使用单个数据库文件
                self.shards['default'] = self.base_db_path
                self.logger.info("使用传统单数据库模式")
            else:
                # 分片模式：初始化分片配置
                self._init_shards()
                self.logger.info(f"分片模式初始化完成，共 {len(self.shards)} 个分片")
            
            # 初始化所有分片的数据库结构
            self._init_all_shard_structures()
            
        except Exception as e:
            self.logger.error(f"分片系统初始化失败: {e}")
            # 降级到传统模式
            self.shard_config.strategy = ShardingStrategy.NONE
            self.shards = {'default': self.base_db_path}
            self.logger.warning("已降级到传统单数据库模式")
    
    def _init_shards(self):
        """
        初始化分片配置
        
        根据分片策略创建相应的分片：
        - 时间分片：按年/月/日创建分片
        - 功能分片：按数据类型创建分片
        - 混合分片：结合时间和功能分片
        """
        if self.shard_config.strategy == ShardingStrategy.TIME_BASED:
            self._init_time_based_shards()
        elif self.shard_config.strategy == ShardingStrategy.FUNCTIONAL:
            self._init_functional_shards()
        elif self.shard_config.strategy == ShardingStrategy.HYBRID:
            self._init_hybrid_shards()
    
    def _init_time_based_shards(self):
        """
        初始化时间分片
        
        保守策略：
        1. 只创建当前时间和前一个时间单位的分片
        2. 历史数据保持在原数据库中
        3. 新数据写入到当前时间分片
        """
        current_time = datetime.now()
        
        if self.shard_config.time_unit == "year":
            # 按年分片
            current_year = current_time.year
            previous_year = current_year - 1
            
            self.shards[f'dial_records_{current_year}'] = f'dial_records_{current_year}.db'
            self.shards[f'dial_records_{previous_year}'] = f'dial_records_{previous_year}.db'
            
        elif self.shard_config.time_unit == "month":
            # 按月分片
            current_month = current_time.strftime('%Y_%m')
            previous_month = (current_time - timedelta(days=30)).strftime('%Y_%m')
            
            self.shards[f'dial_records_{current_month}'] = f'dial_records_{current_month}.db'
            self.shards[f'dial_records_{previous_month}'] = f'dial_records_{previous_month}.db'
        
        # 保留原数据库作为历史数据分片
        self.shards['dial_records_legacy'] = self.base_db_path
        
        self.logger.info(f"时间分片初始化完成，时间单位: {self.shard_config.time_unit}")
    
    def _init_functional_shards(self):
        """
        初始化功能分片
        
        按数据类型分离：
        1. 业务数据：拨号记录等动态数据
        2. 基础数据：归属地等静态数据
        3. 缓存数据：临时查询缓存
        4. 统计数据：性能统计和分析数据
        """
        self.shards = {
            'dial_records': 'dial_records.db',      # 拨号记录（主要业务数据）
            'location_data': 'location_data.db',    # 归属地数据（基础数据）
            'cache_data': 'cache_data.db',          # 缓存数据
            'analytics_data': 'analytics_data.db',  # 统计分析数据
            'legacy_data': self.base_db_path        # 原有数据（兼容性）
        }
        
        self.logger.info("功能分片初始化完成")
    
    def _init_hybrid_shards(self):
        """
        初始化混合分片
        
        结合时间和功能分片：
        1. 按功能分离不同类型的数据
        2. 对于业务数据，再按时间进一步分片
        """
        current_year = datetime.now().year
        
        self.shards = {
            # 业务数据按时间分片
            f'dial_records_{current_year}': f'dial_records_{current_year}.db',
            f'dial_records_{current_year-1}': f'dial_records_{current_year-1}.db',
            
            # 基础数据功能分片
            'location_data': 'location_data.db',
            'cache_data': 'cache_data.db',
            'analytics_data': 'analytics_data.db',
            
            # 历史数据
            'legacy_data': self.base_db_path
        }
        
        self.logger.info("混合分片初始化完成")
    
    def _init_all_shard_structures(self):
        """
        初始化所有分片的数据库结构
        
        保守策略：
        1. 只在需要时创建分片文件
        2. 复制原数据库的表结构
        3. 保持索引和约束一致性
        """
        for shard_name, shard_path in self.shards.items():
            try:
                if shard_path != self.base_db_path:  # 跳过原数据库
                    self._ensure_shard_structure(shard_name, shard_path)
            except Exception as e:
                self.logger.error(f"初始化分片 {shard_name} 失败: {e}")
                # 移除失败的分片，避免影响整体系统
                if shard_name in self.shards:
                    del self.shards[shard_name]
    
    def _ensure_shard_structure(self, shard_name: str, shard_path: str):
        """
        确保分片具有正确的数据库结构
        
        Args:
            shard_name: 分片名称
            shard_path: 分片文件路径
        """
        # 如果分片文件不存在，创建它
        if not os.path.exists(shard_path):
            self.logger.info(f"创建新分片: {shard_name} -> {shard_path}")
            
            # 连接到新分片
            conn = sqlite3.connect(shard_path)
            cursor = conn.cursor()
            
            try:
                # 复制原数据库的表结构
                self._copy_table_structure(cursor, shard_name)
                
                # 应用SQLite优化设置
                self._apply_sqlite_optimizations(cursor)
                
                conn.commit()
                self.logger.info(f"分片 {shard_name} 结构创建完成")
                
            except Exception as e:
                self.logger.error(f"创建分片结构失败: {e}")
                raise
            finally:
                conn.close()
    
    def _copy_table_structure(self, cursor: sqlite3.Cursor, shard_name: str):
        """
        复制表结构到新分片
        
        Args:
            cursor: 目标分片的数据库游标
            shard_name: 分片名称
        """
        # 根据分片类型决定需要创建哪些表
        if 'dial_records' in shard_name:
            # 拨号记录分片：只创建拨号相关的表
            self._create_dial_records_tables(cursor)
        elif 'location' in shard_name:
            # 归属地分片：只创建归属地相关的表
            self._create_location_tables(cursor)
        elif 'cache' in shard_name:
            # 缓存分片：创建缓存表
            self._create_cache_tables(cursor)
        elif 'analytics' in shard_name:
            # 统计分片：创建统计表
            self._create_analytics_tables(cursor)
        else:
            # 默认：创建所有表
            self._create_all_tables(cursor)
    
    def _create_dial_records_tables(self, cursor: sqlite3.Cursor):
        """创建拨号记录相关表"""
        # 主要的拨号记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dial_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone_number TEXT NOT NULL,
                mark_info TEXT,
                province TEXT,
                city TEXT,
                location TEXT,
                isp TEXT,
                location_source TEXT,
                phone_model TEXT,
                android_version TEXT,
                dial_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'success',
                screenshot_path TEXT,
                ocr_text TEXT,
                processing_time REAL,
                method TEXT DEFAULT "ML Kit",
                retry_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_dial_phone_number ON dial_records(phone_number)',
            'CREATE INDEX IF NOT EXISTS idx_dial_time ON dial_records(dial_time)',
            'CREATE INDEX IF NOT EXISTS idx_dial_status ON dial_records(status)',
            'CREATE INDEX IF NOT EXISTS idx_dial_method ON dial_records(method)',
            'CREATE INDEX IF NOT EXISTS idx_dial_province_city ON dial_records(province, city)',
            'CREATE INDEX IF NOT EXISTS idx_dial_phone_time ON dial_records(phone_number, dial_time)',
            'CREATE INDEX IF NOT EXISTS idx_dial_status_time ON dial_records(status, dial_time)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _create_location_tables(self, cursor: sqlite3.Cursor):
        """创建归属地相关表"""
        # 手机号段表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mobile_location (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                segment TEXT NOT NULL,
                phone_prefix TEXT NOT NULL,
                province TEXT NOT NULL,
                city TEXT NOT NULL,
                isp TEXT,
                tel_code TEXT,
                postal_code TEXT,
                area_code TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(segment)
            )
        ''')
        
        # 固话区号表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS landline_location (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                area_code TEXT NOT NULL,
                province TEXT NOT NULL,
                city TEXT NOT NULL,
                main_length INTEGER,
                called_length TEXT,
                region TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(area_code)
            )
        ''')
        
        # 创建索引
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_mobile_segment ON mobile_location(segment)',
            'CREATE INDEX IF NOT EXISTS idx_mobile_province_city ON mobile_location(province, city)',
            'CREATE INDEX IF NOT EXISTS idx_landline_area_code ON landline_location(area_code)',
            'CREATE INDEX IF NOT EXISTS idx_landline_province_city ON landline_location(province, city)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _create_cache_tables(self, cursor: sqlite3.Cursor):
        """创建缓存相关表"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS location_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone_number TEXT NOT NULL,
                phone_type TEXT NOT NULL,
                province TEXT NOT NULL,
                city TEXT NOT NULL,
                isp TEXT,
                area_code TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(phone_number)
            )
        ''')
        
        # 创建索引
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_cache_phone ON location_cache(phone_number)',
            'CREATE INDEX IF NOT EXISTS idx_cache_updated ON location_cache(last_updated)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _create_analytics_tables(self, cursor: sqlite3.Cursor):
        """创建统计分析相关表"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                metric_value REAL NOT NULL,
                metric_type TEXT NOT NULL,
                recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS query_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query_type TEXT NOT NULL,
                execution_time REAL NOT NULL,
                shard_name TEXT,
                recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_perf_metric ON performance_stats(metric_name, recorded_at)',
            'CREATE INDEX IF NOT EXISTS idx_query_type ON query_performance(query_type, recorded_at)',
            'CREATE INDEX IF NOT EXISTS idx_query_shard ON query_performance(shard_name, recorded_at)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _create_all_tables(self, cursor: sqlite3.Cursor):
        """创建所有表（用于默认分片）"""
        self._create_dial_records_tables(cursor)
        self._create_location_tables(cursor)
        self._create_cache_tables(cursor)
        self._create_analytics_tables(cursor)
    
    def _apply_sqlite_optimizations(self, cursor: sqlite3.Cursor):
        """
        应用SQLite优化设置
        
        Args:
            cursor: 数据库游标
        """
        optimizations = [
            'PRAGMA journal_mode=WAL',        # 写前日志模式
            'PRAGMA synchronous=NORMAL',      # 平衡安全性和性能
            'PRAGMA cache_size=10000',        # 增加缓存大小
            'PRAGMA temp_store=MEMORY',       # 临时表存储在内存中
            'PRAGMA mmap_size=268435456',     # 256MB内存映射
            'PRAGMA optimize'                 # 优化查询计划
        ]
        
        for pragma in optimizations:
            try:
                cursor.execute(pragma)
            except Exception as e:
                self.logger.warning(f"应用优化设置失败 {pragma}: {e}")

    @contextmanager
    def _get_shard_connection(self, shard_name: str):
        """
        获取指定分片的数据库连接

        Args:
            shard_name: 分片名称

        Yields:
            sqlite3.Connection: 数据库连接对象
        """
        if shard_name not in self.shards:
            raise ValueError(f"分片 {shard_name} 不存在")

        shard_path = self.shards[shard_name]

        # 使用连接缓存
        cache_key = f"{threading.current_thread().ident}_{shard_name}"

        if cache_key not in self.shard_connections:
            conn = sqlite3.connect(
                shard_path,
                check_same_thread=False,
                timeout=30.0
            )
            conn.row_factory = sqlite3.Row

            # 应用优化设置
            cursor = conn.cursor()
            self._apply_sqlite_optimizations(cursor)

            self.shard_connections[cache_key] = conn

        try:
            yield self.shard_connections[cache_key]
        except Exception as e:
            self.logger.error(f"分片 {shard_name} 操作失败: {e}")
            # 清理可能损坏的连接
            if cache_key in self.shard_connections:
                try:
                    self.shard_connections[cache_key].close()
                except:
                    pass
                del self.shard_connections[cache_key]
            raise

    def _route_query_to_shard(self, operation_type: str, record_data: Optional[Dict] = None) -> str:
        """
        智能查询路由：根据操作类型和数据特征路由到合适的分片

        Args:
            operation_type: 操作类型 ('read', 'write', 'update', 'delete')
            record_data: 记录数据（用于写操作的路由决策）

        Returns:
            str: 目标分片名称
        """
        if self.shard_config.strategy == ShardingStrategy.NONE:
            return 'default'

        # 写操作路由
        if operation_type in ['write', 'insert'] and record_data:
            return self._route_write_operation(record_data)

        # 读操作路由
        elif operation_type in ['read', 'select']:
            return self._route_read_operation(record_data)

        # 更新和删除操作路由
        elif operation_type in ['update', 'delete']:
            return self._route_modify_operation(record_data)

        # 默认路由到主分片
        return self._get_primary_shard()

    def _route_write_operation(self, record_data: Dict) -> str:
        """
        路由写操作到合适的分片

        Args:
            record_data: 要写入的记录数据

        Returns:
            str: 目标分片名称
        """
        if self.shard_config.strategy == ShardingStrategy.TIME_BASED:
            # 时间分片：根据记录时间路由
            record_time = record_data.get('dial_time', datetime.now())
            if isinstance(record_time, str):
                record_time = datetime.fromisoformat(record_time.replace('Z', '+00:00'))

            if self.shard_config.time_unit == "year":
                shard_name = f'dial_records_{record_time.year}'
            elif self.shard_config.time_unit == "month":
                shard_name = f'dial_records_{record_time.strftime("%Y_%m")}'
            else:
                shard_name = f'dial_records_{record_time.strftime("%Y_%m_%d")}'

            # 如果目标分片不存在且允许自动创建，则创建它
            if shard_name not in self.shards and self.shard_config.auto_create_shards:
                self._create_time_shard(shard_name, record_time)

            return shard_name if shard_name in self.shards else 'dial_records_legacy'

        elif self.shard_config.strategy == ShardingStrategy.FUNCTIONAL:
            # 功能分片：根据数据类型路由
            if 'phone_number' in record_data:
                return 'dial_records'
            elif 'segment' in record_data:
                return 'location_data'
            else:
                return 'legacy_data'

        elif self.shard_config.strategy == ShardingStrategy.HYBRID:
            # 混合分片：结合时间和功能
            if 'phone_number' in record_data:
                # 拨号记录按时间分片
                record_time = record_data.get('dial_time', datetime.now())
                if isinstance(record_time, str):
                    record_time = datetime.fromisoformat(record_time.replace('Z', '+00:00'))

                shard_name = f'dial_records_{record_time.year}'
                return shard_name if shard_name in self.shards else 'legacy_data'
            else:
                # 其他数据按功能分片
                return 'location_data'

        return self._get_primary_shard()

    def _route_read_operation(self, query_params: Optional[Dict] = None) -> Union[str, List[str]]:
        """
        路由读操作到合适的分片

        Args:
            query_params: 查询参数

        Returns:
            Union[str, List[str]]: 单个分片名称或分片名称列表（跨分片查询）
        """
        if not query_params:
            # 无特定查询条件，可能需要跨分片查询
            if self.shard_config.enable_cross_shard_queries:
                return self._get_all_data_shards()
            else:
                return self._get_primary_shard()

        # 根据查询条件路由
        if 'time_range' in query_params:
            # 时间范围查询
            return self._get_shards_for_time_range(query_params['time_range'])
        elif 'phone_number' in query_params:
            # 特定号码查询
            return self._get_shards_for_phone_query()
        else:
            return self._get_primary_shard()

    def _create_time_shard(self, shard_name: str, record_time: datetime):
        """
        创建新的时间分片

        Args:
            shard_name: 分片名称
            record_time: 记录时间
        """
        try:
            shard_path = f'{shard_name}.db'
            self.shards[shard_name] = shard_path
            self._ensure_shard_structure(shard_name, shard_path)
            self.logger.info(f"自动创建时间分片: {shard_name}")
        except Exception as e:
            self.logger.error(f"创建时间分片失败: {e}")

    def _get_primary_shard(self) -> str:
        """获取主分片名称"""
        if 'default' in self.shards:
            return 'default'
        elif 'dial_records' in self.shards:
            return 'dial_records'
        elif 'legacy_data' in self.shards:
            return 'legacy_data'
        else:
            return list(self.shards.keys())[0]

    def _get_all_data_shards(self) -> List[str]:
        """获取所有包含业务数据的分片"""
        data_shards = []
        for shard_name in self.shards.keys():
            if any(keyword in shard_name for keyword in ['dial_records', 'default', 'legacy']):
                data_shards.append(shard_name)
        return data_shards or [self._get_primary_shard()]

    def _get_shards_for_time_range(self, time_range: Dict) -> List[str]:
        """
        获取时间范围内的所有相关分片

        Args:
            time_range: 时间范围 {'start': datetime, 'end': datetime}

        Returns:
            List[str]: 相关分片列表
        """
        if self.shard_config.strategy not in [ShardingStrategy.TIME_BASED, ShardingStrategy.HYBRID]:
            return [self._get_primary_shard()]

        start_time = time_range.get('start')
        end_time = time_range.get('end')

        if not start_time or not end_time:
            return self._get_all_data_shards()

        relevant_shards = []

        if self.shard_config.time_unit == "year":
            for year in range(start_time.year, end_time.year + 1):
                shard_name = f'dial_records_{year}'
                if shard_name in self.shards:
                    relevant_shards.append(shard_name)

        # 如果没有找到相关分片，返回所有数据分片
        return relevant_shards or self._get_all_data_shards()

    def _get_shards_for_phone_query(self) -> List[str]:
        """获取电话号码查询相关的分片"""
        return self._get_all_data_shards()

    @contextmanager
    def _timed_operation(self, operation_type: str, shard_name: str = "unknown"):
        """
        操作性能计时上下文管理器

        Args:
            operation_type: 操作类型
            shard_name: 分片名称
        """
        start_time = time.time()
        try:
            yield
        finally:
            elapsed = time.time() - start_time

            # 更新统计信息
            with self._lock:
                self.stats['total_queries'] += 1
                self.stats['total_time'] += elapsed

                # 分片统计
                if shard_name not in self.stats['shard_queries']:
                    self.stats['shard_queries'][shard_name] = {
                        'count': 0,
                        'total_time': 0.0,
                        'avg_time': 0.0
                    }

                shard_stats = self.stats['shard_queries'][shard_name]
                shard_stats['count'] += 1
                shard_stats['total_time'] += elapsed
                shard_stats['avg_time'] = shard_stats['total_time'] / shard_stats['count']

                # 慢查询检测
                if elapsed > 0.1:  # 100ms以上认为是慢查询
                    self.stats['slow_queries'] += 1
                    self.logger.warning(f"慢查询检测: {operation_type} 在分片 {shard_name} 耗时 {elapsed:.3f}秒")

    def save_dial_record(self, record: Dict) -> bool:
        """
        保存拨号记录（兼容原有接口）

        Args:
            record: 拨号记录数据

        Returns:
            bool: 保存是否成功
        """
        try:
            # 路由到合适的分片
            target_shard = self._route_query_to_shard('write', record)

            with self._timed_operation('save_dial_record', target_shard):
                with self._get_shard_connection(target_shard) as conn:
                    cursor = conn.cursor()

                    # 检查表结构
                    cursor.execute("PRAGMA table_info(dial_records)")
                    columns = [column[1] for column in cursor.fetchall()]

                    # 构建插入语句
                    if self._has_extended_columns(columns):
                        # 使用扩展字段
                        cursor.execute('''
                            INSERT INTO dial_records
                            (phone_number, mark_info, province, city, location, isp, location_source,
                             phone_model, android_version, status, screenshot_path, processing_time,
                             method, retry_count, dial_time)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            record['phone_number'],
                            record.get('mark_info', ''),
                            record.get('province', ''),
                            record.get('city', ''),
                            record.get('location', ''),
                            record.get('isp', ''),
                            record.get('location_source', ''),
                            record.get('phone_model', ''),
                            record.get('android_version', ''),
                            record.get('status', 'success'),
                            record.get('screenshot_path', ''),
                            record.get('processing_time', 0.0),
                            record.get('method', 'ML Kit'),
                            record.get('retry_count', 0),
                            record.get('dial_time', datetime.now().isoformat())
                        ))
                    else:
                        # 使用基础字段
                        cursor.execute('''
                            INSERT INTO dial_records
                            (phone_number, mark_info, phone_model, android_version, status,
                             screenshot_path, processing_time, method)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            record['phone_number'],
                            record.get('mark_info', ''),
                            record.get('phone_model', ''),
                            record.get('android_version', ''),
                            record.get('status', 'success'),
                            record.get('screenshot_path', ''),
                            record.get('processing_time', 0.0),
                            record.get('method', 'ML Kit')
                        ))

                    conn.commit()
                    self.logger.debug(f"记录已保存到分片 {target_shard}: {record['phone_number']}")
                    return True

        except Exception as e:
            self.logger.error(f"保存拨号记录失败: {e}")
            return False

    def _has_extended_columns(self, columns: List[str]) -> bool:
        """
        检查表是否包含扩展字段

        Args:
            columns: 表字段列表

        Returns:
            bool: 是否包含扩展字段
        """
        extended_fields = ['province', 'city', 'isp', 'location_source']
        return all(field in columns for field in extended_fields)

    def get_recent_records(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """
        获取最近的记录（兼容原有接口）

        Args:
            limit: 记录数量限制
            offset: 偏移量

        Returns:
            List[Dict]: 记录列表
        """
        try:
            # 确定查询的分片
            target_shards = self._route_read_operation()
            if isinstance(target_shards, str):
                target_shards = [target_shards]

            all_records = []

            for shard_name in target_shards:
                try:
                    with self._timed_operation('get_recent_records', shard_name):
                        with self._get_shard_connection(shard_name) as conn:
                            cursor = conn.cursor()

                            cursor.execute('''
                                SELECT * FROM dial_records
                                ORDER BY dial_time DESC, id DESC
                                LIMIT ? OFFSET ?
                            ''', (limit, offset))

                            records = [dict(row) for row in cursor.fetchall()]
                            all_records.extend(records)

                except Exception as e:
                    self.logger.warning(f"从分片 {shard_name} 查询记录失败: {e}")
                    continue

            # 如果是跨分片查询，需要重新排序和限制
            if len(target_shards) > 1:
                all_records.sort(key=lambda x: x.get('dial_time', ''), reverse=True)
                all_records = all_records[offset:offset + limit]

            self.logger.debug(f"查询到 {len(all_records)} 条记录")
            return all_records

        except Exception as e:
            self.logger.error(f"查询最近记录失败: {e}")
            return []

    def get_records_by_phone(self, phone_number: str) -> List[Dict]:
        """
        根据电话号码查询记录（兼容原有接口）

        Args:
            phone_number: 电话号码

        Returns:
            List[Dict]: 记录列表
        """
        try:
            target_shards = self._route_read_operation({'phone_number': phone_number})
            if isinstance(target_shards, str):
                target_shards = [target_shards]

            all_records = []

            for shard_name in target_shards:
                try:
                    with self._timed_operation('get_records_by_phone', shard_name):
                        with self._get_shard_connection(shard_name) as conn:
                            cursor = conn.cursor()

                            cursor.execute('''
                                SELECT * FROM dial_records
                                WHERE phone_number = ?
                                ORDER BY dial_time DESC
                            ''', (phone_number,))

                            records = [dict(row) for row in cursor.fetchall()]
                            all_records.extend(records)

                except Exception as e:
                    self.logger.warning(f"从分片 {shard_name} 查询号码记录失败: {e}")
                    continue

            # 跨分片查询结果排序
            if len(target_shards) > 1:
                all_records.sort(key=lambda x: x.get('dial_time', ''), reverse=True)

            return all_records

        except Exception as e:
            self.logger.error(f"根据号码查询记录失败: {e}")
            return []

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息（兼容原有接口）

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {
                'total_records': 0,
                'mark_distribution': {},
                'province_distribution': {},
                'city_distribution': {},
                'recent_activity': []
            }

            # 从所有数据分片收集统计信息
            data_shards = self._get_all_data_shards()

            for shard_name in data_shards:
                try:
                    with self._timed_operation('get_statistics', shard_name):
                        with self._get_shard_connection(shard_name) as conn:
                            cursor = conn.cursor()

                            # 总记录数
                            cursor.execute('SELECT COUNT(*) FROM dial_records')
                            count = cursor.fetchone()[0]
                            stats['total_records'] += count

                            # 标记分布
                            cursor.execute('''
                                SELECT mark_info, COUNT(*) as count
                                FROM dial_records
                                WHERE mark_info IS NOT NULL AND mark_info != ''
                                GROUP BY mark_info
                            ''')

                            for row in cursor.fetchall():
                                mark_info = row[0]
                                count = row[1]
                                if mark_info in stats['mark_distribution']:
                                    stats['mark_distribution'][mark_info] += count
                                else:
                                    stats['mark_distribution'][mark_info] = count

                            # 省份分布
                            cursor.execute('''
                                SELECT province, COUNT(*) as count
                                FROM dial_records
                                WHERE province IS NOT NULL AND province != ''
                                GROUP BY province
                            ''')

                            for row in cursor.fetchall():
                                province = row[0]
                                count = row[1]
                                if province in stats['province_distribution']:
                                    stats['province_distribution'][province] += count
                                else:
                                    stats['province_distribution'][province] = count

                            # 城市分布
                            cursor.execute('''
                                SELECT city, COUNT(*) as count
                                FROM dial_records
                                WHERE city IS NOT NULL AND city != ''
                                GROUP BY city
                            ''')

                            for row in cursor.fetchall():
                                city = row[0]
                                count = row[1]
                                if city in stats['city_distribution']:
                                    stats['city_distribution'][city] += count
                                else:
                                    stats['city_distribution'][city] = count

                except Exception as e:
                    self.logger.warning(f"从分片 {shard_name} 收集统计信息失败: {e}")
                    continue

            return stats

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计信息
        """
        with self._lock:
            total_queries = self.stats['total_queries']

            performance_stats = {
                'total_queries': total_queries,
                'total_time': self.stats['total_time'],
                'avg_query_time': (self.stats['total_time'] / max(total_queries, 1)) * 1000,  # ms
                'slow_queries': self.stats['slow_queries'],
                'slow_query_rate': (self.stats['slow_queries'] / max(total_queries, 1)) * 100,  # %
                'cache_hits': self.stats['cache_hits'],
                'cache_misses': self.stats['cache_misses'],
                'cache_hit_rate': (self.stats['cache_hits'] / max(self.stats['cache_hits'] + self.stats['cache_misses'], 1)) * 100,  # %
                'cross_shard_queries': self.stats['cross_shard_queries'],
                'shard_performance': {}
            }

            # 各分片性能统计
            for shard_name, shard_stats in self.stats['shard_queries'].items():
                performance_stats['shard_performance'][shard_name] = {
                    'query_count': shard_stats['count'],
                    'total_time': shard_stats['total_time'],
                    'avg_time_ms': shard_stats['avg_time'] * 1000,
                    'query_percentage': (shard_stats['count'] / max(total_queries, 1)) * 100
                }

            # 分片系统信息
            performance_stats['sharding_info'] = {
                'strategy': self.shard_config.strategy.value,
                'total_shards': len(self.shards),
                'active_connections': len(self.shard_connections),
                'shard_list': list(self.shards.keys())
            }

            return performance_stats

    def optimize_database(self):
        """
        优化所有分片数据库
        """
        for shard_name, shard_path in self.shards.items():
            try:
                self.logger.info(f"优化分片: {shard_name}")

                with self._get_shard_connection(shard_name) as conn:
                    cursor = conn.cursor()

                    # 分析表统计信息
                    cursor.execute('ANALYZE')

                    # 重建索引
                    cursor.execute('REINDEX')

                    # 清理碎片（小心使用，可能耗时较长）
                    cursor.execute('PRAGMA optimize')

                    conn.commit()

                self.logger.info(f"分片 {shard_name} 优化完成")

            except Exception as e:
                self.logger.error(f"优化分片 {shard_name} 失败: {e}")

    def close_all_connections(self):
        """
        关闭所有数据库连接
        """
        for cache_key, conn in self.shard_connections.items():
            try:
                conn.close()
                self.logger.debug(f"关闭连接: {cache_key}")
            except Exception as e:
                self.logger.warning(f"关闭连接失败 {cache_key}: {e}")

        self.shard_connections.clear()
        self.logger.info("所有数据库连接已关闭")

    def get_shard_info(self) -> Dict[str, Any]:
        """
        获取分片信息

        Returns:
            Dict[str, Any]: 分片信息
        """
        shard_info = {
            'strategy': self.shard_config.strategy.value,
            'total_shards': len(self.shards),
            'shards': {}
        }

        for shard_name, shard_path in self.shards.items():
            try:
                # 获取文件大小
                file_size = os.path.getsize(shard_path) if os.path.exists(shard_path) else 0

                # 获取记录数（如果是数据分片）
                record_count = 0
                if any(keyword in shard_name for keyword in ['dial_records', 'default', 'legacy']):
                    try:
                        with self._get_shard_connection(shard_name) as conn:
                            cursor = conn.cursor()
                            cursor.execute('SELECT COUNT(*) FROM dial_records')
                            record_count = cursor.fetchone()[0]
                    except:
                        pass

                shard_info['shards'][shard_name] = {
                    'path': shard_path,
                    'file_size_mb': round(file_size / (1024 * 1024), 2),
                    'record_count': record_count,
                    'exists': os.path.exists(shard_path)
                }

            except Exception as e:
                self.logger.warning(f"获取分片 {shard_name} 信息失败: {e}")
                shard_info['shards'][shard_name] = {
                    'path': shard_path,
                    'error': str(e)
                }

        return shard_info


def test_advanced_sqlite_manager():
    """测试高级SQLite管理器"""
    print("=== 测试高级SQLite管理器 ===")

    # 测试传统模式
    print("\n1. 测试传统模式（无分片）")
    config_none = ShardConfig(strategy=ShardingStrategy.NONE)
    manager_none = AdvancedSQLiteManager(shard_config=config_none)
    print(f"分片数量: {len(manager_none.shards)}")
    print(f"分片列表: {list(manager_none.shards.keys())}")

    # 测试时间分片
    print("\n2. 测试时间分片模式")
    config_time = ShardConfig(
        strategy=ShardingStrategy.TIME_BASED,
        time_unit="year",
        auto_create_shards=True
    )
    manager_time = AdvancedSQLiteManager(shard_config=config_time)
    print(f"分片数量: {len(manager_time.shards)}")
    print(f"分片列表: {list(manager_time.shards.keys())}")

    # 测试功能分片
    print("\n3. 测试功能分片模式")
    config_func = ShardConfig(strategy=ShardingStrategy.FUNCTIONAL)
    manager_func = AdvancedSQLiteManager(shard_config=config_func)
    print(f"分片数量: {len(manager_func.shards)}")
    print(f"分片列表: {list(manager_func.shards.keys())}")

    # 测试混合分片
    print("\n4. 测试混合分片模式")
    config_hybrid = ShardConfig(strategy=ShardingStrategy.HYBRID)
    manager_hybrid = AdvancedSQLiteManager(shard_config=config_hybrid)
    print(f"分片数量: {len(manager_hybrid.shards)}")
    print(f"分片列表: {list(manager_hybrid.shards.keys())}")

    print("\n✅ 高级SQLite管理器测试完成")


if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    test_advanced_sqlite_manager()
