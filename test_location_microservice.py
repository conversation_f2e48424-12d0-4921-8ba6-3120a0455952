"""
归属地微服务测试
测试微服务的各种功能，包括API接口、性能、错误处理等
"""

import sys
import os
import time
import logging
import asyncio
import json
from typing import Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_microservice_initialization():
    """测试微服务初始化"""
    print("=== 测试微服务初始化 ===")
    
    try:
        from location_microservice import LocationMicroservice
        
        # 测试默认配置初始化
        print("1. 测试默认配置初始化")
        service = LocationMicroservice()
        print(f"  ✓ 服务初始化成功")
        print(f"  - 配置: {service.config['host']}:{service.config['port']}")
        print(f"  - LocationManager可用: {service.location_manager is not None}")
        print(f"  - FastAPI可用: {service.app is not None}")
        
        # 测试自定义配置初始化
        print("\n2. 测试自定义配置初始化")
        custom_config = {
            'host': '0.0.0.0',
            'port': 8002,
            'debug': True,
            'cache_enabled': False
        }
        service_custom = LocationMicroservice(custom_config)
        print(f"  ✓ 自定义配置初始化成功")
        print(f"  - 配置: {service_custom.config['host']}:{service_custom.config['port']}")
        print(f"  - 调试模式: {service_custom.config['debug']}")
        print(f"  - 缓存启用: {service_custom.config['cache_enabled']}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 微服务初始化失败: {e}")
        return False

def test_sync_location_query():
    """测试同步归属地查询"""
    print("\n=== 测试同步归属地查询 ===")
    
    try:
        from location_microservice import LocationMicroservice
        
        service = LocationMicroservice()
        
        # 测试用例
        test_cases = [
            ('13800138000', '手机号码'),
            ('13900139000', '手机号码'),
            ('02012345678', '固话号码'),
            ('invalid', '无效号码'),
            ('', '空号码')
        ]
        
        print("同步查询测试:")
        success_count = 0
        
        for phone_number, description in test_cases:
            try:
                start_time = time.time()
                result = service.get_phone_location(phone_number)
                query_time = (time.time() - start_time) * 1000
                
                if result:
                    success_count += 1
                    print(f"  ✓ {description} ({phone_number}): {result.get('province', 'N/A')} {result.get('city', 'N/A')} - {query_time:.2f}ms")
                else:
                    print(f"  - {description} ({phone_number}): 未找到归属地信息 - {query_time:.2f}ms")
                    
            except Exception as e:
                print(f"  ✗ {description} ({phone_number}): 查询失败 - {e}")
        
        print(f"\n查询成功率: {success_count}/{len(test_cases)}")
        return success_count > 0
        
    except Exception as e:
        print(f"  ✗ 同步查询测试失败: {e}")
        return False

def test_async_location_query():
    """测试异步归属地查询"""
    print("\n=== 测试异步归属地查询 ===")
    
    try:
        from location_microservice import LocationMicroservice
        
        service = LocationMicroservice()
        
        async def async_test():
            test_cases = [
                '13800138000',
                '13900139000',
                '15800158000'
            ]
            
            print("异步查询测试:")
            results = []
            
            for phone_number in test_cases:
                try:
                    result = await service._process_location_query(phone_number, True)
                    results.append(result)
                    
                    status_icon = "✓" if result.status == "success" else "✗"
                    print(f"  {status_icon} {phone_number}: {result.status} - {result.processing_time_ms:.2f}ms")
                    
                    if result.data:
                        print(f"    归属地: {result.data.get('province', 'N/A')} {result.data.get('city', 'N/A')}")
                    
                except Exception as e:
                    print(f"  ✗ {phone_number}: 异步查询失败 - {e}")
            
            return len(results)
        
        # 运行异步测试
        result_count = asyncio.run(async_test())
        print(f"\n异步查询完成: {result_count} 个结果")
        
        return result_count > 0
        
    except Exception as e:
        print(f"  ✗ 异步查询测试失败: {e}")
        return False

def test_validation_and_error_handling():
    """测试验证和错误处理"""
    print("\n=== 测试验证和错误处理 ===")
    
    try:
        from location_microservice import LocationMicroservice
        
        service = LocationMicroservice()
        
        # 测试号码验证
        print("1. 电话号码验证测试:")
        validation_cases = [
            ('13800138000', True, '有效手机号'),
            ('02012345678', True, '有效固话'),
            ('1380013800', False, '手机号位数不足'),
            ('abc123', False, '包含字母'),
            ('', False, '空号码'),
            ('+8613800138000', True, '带国际前缀'),
            ('138-0013-8000', True, '带分隔符')
        ]
        
        for phone, expected, description in validation_cases:
            result = service._validate_phone_number(phone)
            status = "✓" if result == expected else "✗"
            print(f"  {status} {description}: {phone} -> {result}")
        
        # 测试异常处理
        print("\n2. 异常处理测试:")
        
        async def error_test():
            try:
                # 测试无效号码
                result = await service._process_location_query("invalid_phone", True)
                print(f"  ✓ 无效号码处理: {result.status} - {result.message}")
                
                # 测试空号码
                result = await service._process_location_query("", True)
                print(f"  ✓ 空号码处理: {result.status} - {result.message}")
                
                return True
                
            except Exception as e:
                print(f"  ✗ 异常处理测试失败: {e}")
                return False
        
        error_result = asyncio.run(error_test())
        
        return error_result
        
    except Exception as e:
        print(f"  ✗ 验证和错误处理测试失败: {e}")
        return False

def test_performance_and_caching():
    """测试性能和缓存"""
    print("\n=== 测试性能和缓存 ===")
    
    try:
        from location_microservice import LocationMicroservice
        
        service = LocationMicroservice()
        
        # 性能测试
        print("1. 性能测试:")
        test_phone = '13800138000'
        query_times = []
        
        for i in range(10):
            start_time = time.time()
            result = service.get_phone_location(test_phone)
            query_time = (time.time() - start_time) * 1000
            query_times.append(query_time)
        
        avg_time = sum(query_times) / len(query_times)
        min_time = min(query_times)
        max_time = max(query_times)
        
        print(f"  查询次数: {len(query_times)}")
        print(f"  平均时间: {avg_time:.2f}ms")
        print(f"  最快时间: {min_time:.2f}ms")
        print(f"  最慢时间: {max_time:.2f}ms")
        
        # 并发测试
        print("\n2. 并发测试:")
        
        async def concurrent_test():
            test_phones = ['13800138000', '13900139000', '15800158000'] * 5
            
            start_time = time.time()
            tasks = [service._process_location_query(phone, True) for phone in test_phones]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = (time.time() - start_time) * 1000
            
            success_count = sum(1 for r in results if hasattr(r, 'status') and r.status == 'success')
            
            print(f"  并发查询数: {len(test_phones)}")
            print(f"  成功查询数: {success_count}")
            print(f"  总耗时: {total_time:.2f}ms")
            print(f"  平均每个查询: {total_time/len(test_phones):.2f}ms")
            
            return success_count > 0
        
        concurrent_result = asyncio.run(concurrent_test())
        
        # 统计信息
        print("\n3. 服务统计:")
        print(f"  总请求数: {service.total_requests}")
        print(f"  缓存命中: {service.cache_hits}")
        print(f"  缓存未命中: {service.cache_misses}")
        if service.total_requests > 0:
            hit_rate = (service.cache_hits / service.total_requests) * 100
            print(f"  缓存命中率: {hit_rate:.1f}%")
        
        return avg_time < 100 and concurrent_result  # 要求平均查询时间小于100ms
        
    except Exception as e:
        print(f"  ✗ 性能和缓存测试失败: {e}")
        return False

def test_mock_data_functionality():
    """测试模拟数据功能"""
    print("\n=== 测试模拟数据功能 ===")
    
    try:
        from location_microservice import LocationMicroservice
        
        # 创建没有LocationManager的服务实例
        service = LocationMicroservice()
        
        # 强制使用模拟数据
        original_manager = service.location_manager
        service.location_manager = None
        
        print("模拟数据测试:")
        test_cases = [
            '13800138000',  # 应该返回北京
            '13900139000',  # 应该返回深圳
            '15800158000'   # 应该返回未知
        ]
        
        for phone_number in test_cases:
            mock_data = service._get_mock_location_data(phone_number)
            print(f"  {phone_number}: {mock_data['province']} {mock_data['city']} ({mock_data['source']})")
        
        # 恢复原始管理器
        service.location_manager = original_manager
        
        return True
        
    except Exception as e:
        print(f"  ✗ 模拟数据测试失败: {e}")
        return False

def test_service_compatibility():
    """测试服务兼容性"""
    print("\n=== 测试服务兼容性 ===")
    
    try:
        from location_microservice import LocationMicroservice, create_location_microservice
        
        # 测试工厂函数
        print("1. 工厂函数测试:")
        service1 = create_location_microservice()
        print(f"  ✓ 工厂函数创建成功: {type(service1).__name__}")
        
        service2 = create_location_microservice({'port': 8003})
        print(f"  ✓ 带配置的工厂函数创建成功: 端口 {service2.config['port']}")
        
        # 测试向后兼容接口
        print("\n2. 向后兼容接口测试:")
        result = service1.get_phone_location('13800138000')
        if result:
            print(f"  ✓ 兼容接口工作正常: {result.get('province', 'N/A')} {result.get('city', 'N/A')}")
        else:
            print(f"  - 兼容接口返回空结果")
        
        # 测试配置兼容性
        print("\n3. 配置兼容性测试:")
        default_config = service1._load_default_config()
        required_keys = ['host', 'port', 'debug', 'enable_cors', 'cache_enabled']
        
        missing_keys = [key for key in required_keys if key not in default_config]
        if not missing_keys:
            print(f"  ✓ 默认配置完整")
        else:
            print(f"  ✗ 缺少配置项: {missing_keys}")
        
        return len(missing_keys) == 0
        
    except Exception as e:
        print(f"  ✗ 服务兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始归属地微服务功能测试...\n")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_microservice_initialization())
    test_results.append(test_sync_location_query())
    test_results.append(test_async_location_query())
    test_results.append(test_validation_and_error_handling())
    test_results.append(test_performance_and_caching())
    test_results.append(test_mock_data_functionality())
    test_results.append(test_service_compatibility())
    
    # 总结测试结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"\n=== 测试结果总结 ===")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 归属地微服务所有测试通过！")
        print("\n✅ 验证的功能:")
        print("  1. 微服务初始化和配置")
        print("  2. 同步和异步归属地查询")
        print("  3. 输入验证和错误处理")
        print("  4. 性能和并发处理")
        print("  5. 模拟数据功能")
        print("  6. 向后兼容性")
        
        print("\n🚀 微服务特性:")
        print("  - RESTful API接口设计")
        print("  - 异步处理支持")
        print("  - 内置缓存机制")
        print("  - 完善的错误处理")
        print("  - 性能监控和统计")
        print("  - 向后兼容的同步接口")
        
        print("\n📋 下一步:")
        print("  - 可以启动微服务: python3 location_microservice.py")
        print("  - API文档地址: http://127.0.0.1:8001/docs")
        print("  - 健康检查: http://127.0.0.1:8001/health")
        
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        failed_tests = total_tests - passed_tests
        print(f"失败测试数: {failed_tests}")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
