#!/usr/bin/env python3
"""
数据库修复脚本
修复现有数据库的表结构问题
"""

import sqlite3
import os

def fix_database():
    """修复数据库表结构"""
    db_path = "phone_marks.db"
    
    if not os.path.exists(db_path):
        print("数据库文件不存在，无需修复")
        return
    
    print("开始修复数据库...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dial_records'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("dial_records表不存在，创建新表...")
            cursor.execute('''
                CREATE TABLE dial_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    phone_number TEXT,
                    mark_info TEXT,
                    phone_model TEXT,
                    android_version TEXT,
                    status TEXT,
                    screenshot_path TEXT,
                    processing_time REAL,
                    method TEXT DEFAULT "ML Kit",
                    dial_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
        else:
            print("检查并添加缺失的列...")
            # 获取现有列
            cursor.execute("PRAGMA table_info(dial_records)")
            columns = [column[1] for column in cursor.fetchall()]
            
            # 需要添加的列
            required_columns = {
                'method': 'TEXT DEFAULT "ML Kit"',
                'screenshot_path': 'TEXT',
                'processing_time': 'REAL'
            }
            
            for column_name, column_type in required_columns.items():
                if column_name not in columns:
                    print(f"添加列: {column_name}")
                    cursor.execute(f'ALTER TABLE dial_records ADD COLUMN {column_name} {column_type}')
        
        conn.commit()
        print("数据库修复完成！")
        
        # 显示表结构
        cursor.execute("PRAGMA table_info(dial_records)")
        columns = cursor.fetchall()
        print("\n当前表结构:")
        for column in columns:
            print(f"  {column[1]} ({column[2]})")
        
        # 显示记录数
        cursor.execute("SELECT COUNT(*) FROM dial_records")
        count = cursor.fetchone()[0]
        print(f"\n总记录数: {count}")
        
        conn.close()
        
    except Exception as e:
        print(f"修复数据库时出错: {e}")
        if conn:
            conn.close()

if __name__ == "__main__":
    fix_database() 