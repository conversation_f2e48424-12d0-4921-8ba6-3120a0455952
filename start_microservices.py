"""
微服务启动脚本
用于启动和管理所有微服务的统一脚本
支持单独启动、批量启动、健康检查等功能
"""

import os
import sys
import time
import logging
import subprocess
import threading
import signal
import json
from typing import Dict, List, Optional
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class MicroserviceManager:
    """微服务管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.processes = {}
        self.services_config = {
            'location': {
                'script': 'location_microservice.py',
                'port': 8001,
                'description': '归属地查询微服务'
            },
            'nlp': {
                'script': 'nlp_microservice.py',
                'port': 8002,
                'description': 'NLP文本分析微服务'
            },
            'gateway': {
                'script': 'api_gateway.py',
                'port': 8000,
                'description': 'API网关'
            }
        }
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info("接收到停止信号，正在关闭所有服务...")
        self.stop_all_services()
        sys.exit(0)
    
    def start_service(self, service_name: str) -> bool:
        """
        启动指定服务
        
        Args:
            service_name: 服务名称
            
        Returns:
            是否启动成功
        """
        if service_name not in self.services_config:
            self.logger.error(f"未知服务: {service_name}")
            return False
        
        if service_name in self.processes:
            self.logger.warning(f"服务 {service_name} 已经在运行")
            return True
        
        config = self.services_config[service_name]
        script_path = config['script']
        
        if not os.path.exists(script_path):
            self.logger.error(f"服务脚本不存在: {script_path}")
            return False
        
        try:
            self.logger.info(f"启动服务: {service_name} ({config['description']})")
            self.logger.info(f"端口: {config['port']}, 脚本: {script_path}")
            
            # 启动服务进程
            process = subprocess.Popen(
                [sys.executable, script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes[service_name] = {
                'process': process,
                'config': config,
                'start_time': time.time()
            }
            
            # 等待一下确保服务启动
            time.sleep(2)
            
            # 检查进程是否还在运行
            if process.poll() is None:
                self.logger.info(f"✓ 服务 {service_name} 启动成功 (PID: {process.pid})")
                return True
            else:
                # 获取错误信息
                stdout, stderr = process.communicate()
                self.logger.error(f"✗ 服务 {service_name} 启动失败")
                if stderr:
                    self.logger.error(f"错误信息: {stderr}")
                del self.processes[service_name]
                return False
                
        except Exception as e:
            self.logger.error(f"启动服务 {service_name} 时发生异常: {e}")
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """
        停止指定服务
        
        Args:
            service_name: 服务名称
            
        Returns:
            是否停止成功
        """
        if service_name not in self.processes:
            self.logger.warning(f"服务 {service_name} 未在运行")
            return True
        
        try:
            process_info = self.processes[service_name]
            process = process_info['process']
            
            self.logger.info(f"停止服务: {service_name}")
            
            # 尝试优雅关闭
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=10)
                self.logger.info(f"✓ 服务 {service_name} 已停止")
            except subprocess.TimeoutExpired:
                # 强制杀死进程
                self.logger.warning(f"服务 {service_name} 未响应，强制终止")
                process.kill()
                process.wait()
                self.logger.info(f"✓ 服务 {service_name} 已强制终止")
            
            del self.processes[service_name]
            return True
            
        except Exception as e:
            self.logger.error(f"停止服务 {service_name} 时发生异常: {e}")
            return False
    
    def restart_service(self, service_name: str) -> bool:
        """
        重启指定服务
        
        Args:
            service_name: 服务名称
            
        Returns:
            是否重启成功
        """
        self.logger.info(f"重启服务: {service_name}")
        
        # 先停止服务
        if not self.stop_service(service_name):
            return False
        
        # 等待一下
        time.sleep(1)
        
        # 再启动服务
        return self.start_service(service_name)
    
    def start_all_services(self) -> bool:
        """
        启动所有服务
        
        Returns:
            是否全部启动成功
        """
        self.logger.info("启动所有微服务...")
        
        # 按依赖顺序启动服务
        service_order = ['location', 'nlp', 'gateway']
        
        success_count = 0
        for service_name in service_order:
            if self.start_service(service_name):
                success_count += 1
                # 服务间启动间隔
                time.sleep(3)
            else:
                self.logger.error(f"服务 {service_name} 启动失败，停止后续启动")
                break
        
        total_services = len(service_order)
        self.logger.info(f"服务启动完成: {success_count}/{total_services}")
        
        if success_count == total_services:
            self.logger.info("🎉 所有微服务启动成功！")
            self._print_service_info()
            return True
        else:
            self.logger.warning("⚠️ 部分服务启动失败")
            return False
    
    def stop_all_services(self) -> bool:
        """
        停止所有服务
        
        Returns:
            是否全部停止成功
        """
        self.logger.info("停止所有微服务...")
        
        # 按相反顺序停止服务
        service_names = list(self.processes.keys())
        service_names.reverse()
        
        success_count = 0
        for service_name in service_names:
            if self.stop_service(service_name):
                success_count += 1
        
        total_services = len(service_names)
        self.logger.info(f"服务停止完成: {success_count}/{total_services}")
        
        return success_count == total_services
    
    def get_service_status(self) -> Dict[str, Dict]:
        """
        获取所有服务状态
        
        Returns:
            服务状态字典
        """
        status = {}
        
        for service_name, config in self.services_config.items():
            if service_name in self.processes:
                process_info = self.processes[service_name]
                process = process_info['process']
                
                # 检查进程是否还在运行
                if process.poll() is None:
                    uptime = time.time() - process_info['start_time']
                    status[service_name] = {
                        'status': 'running',
                        'pid': process.pid,
                        'port': config['port'],
                        'uptime': uptime,
                        'description': config['description']
                    }
                else:
                    status[service_name] = {
                        'status': 'stopped',
                        'port': config['port'],
                        'description': config['description']
                    }
                    # 清理已停止的进程
                    del self.processes[service_name]
            else:
                status[service_name] = {
                    'status': 'stopped',
                    'port': config['port'],
                    'description': config['description']
                }
        
        return status
    
    def _print_service_info(self):
        """打印服务信息"""
        print("\n" + "="*60)
        print("微服务状态信息")
        print("="*60)
        
        status = self.get_service_status()
        
        for service_name, info in status.items():
            status_icon = "🟢" if info['status'] == 'running' else "🔴"
            print(f"{status_icon} {service_name.upper()}")
            print(f"   描述: {info['description']}")
            print(f"   端口: {info['port']}")
            print(f"   状态: {info['status']}")
            
            if info['status'] == 'running':
                print(f"   PID: {info['pid']}")
                uptime_str = f"{int(info['uptime'])}秒"
                print(f"   运行时间: {uptime_str}")
                print(f"   访问地址: http://127.0.0.1:{info['port']}")
                if service_name == 'gateway':
                    print(f"   API文档: http://127.0.0.1:{info['port']}/docs")
            print()
        
        if any(info['status'] == 'running' for info in status.values()):
            print("💡 使用 Ctrl+C 停止所有服务")
        
        print("="*60)
    
    def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            所有服务是否健康
        """
        try:
            import httpx
            
            self.logger.info("执行服务健康检查...")
            
            status = self.get_service_status()
            healthy_services = 0
            total_services = 0
            
            for service_name, info in status.items():
                if info['status'] == 'running':
                    total_services += 1
                    port = info['port']
                    
                    try:
                        # 发送健康检查请求
                        response = httpx.get(f"http://127.0.0.1:{port}/health", timeout=5.0)
                        
                        if response.status_code == 200:
                            healthy_services += 1
                            self.logger.info(f"✓ {service_name} 健康检查通过")
                        else:
                            self.logger.warning(f"✗ {service_name} 健康检查失败: HTTP {response.status_code}")
                    
                    except Exception as e:
                        self.logger.warning(f"✗ {service_name} 健康检查失败: {e}")
            
            self.logger.info(f"健康检查完成: {healthy_services}/{total_services} 服务健康")
            return healthy_services == total_services
            
        except ImportError:
            self.logger.warning("httpx不可用，跳过健康检查")
            return True
    
    def monitor_services(self, interval: int = 30):
        """
        监控服务状态
        
        Args:
            interval: 监控间隔（秒）
        """
        self.logger.info(f"开始监控服务状态，间隔: {interval}秒")
        
        try:
            while True:
                time.sleep(interval)
                
                # 检查服务状态
                status = self.get_service_status()
                running_count = sum(1 for info in status.values() if info['status'] == 'running')
                total_count = len(status)
                
                self.logger.info(f"服务状态检查: {running_count}/{total_count} 服务运行中")
                
                # 如果有服务停止，尝试重启
                for service_name, info in status.items():
                    if info['status'] == 'stopped' and service_name in self.services_config:
                        self.logger.warning(f"检测到服务 {service_name} 已停止，尝试重启...")
                        self.start_service(service_name)
                
        except KeyboardInterrupt:
            self.logger.info("监控已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微服务管理器')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status', 'health', 'monitor'], 
                       help='操作类型')
    parser.add_argument('--service', '-s', help='指定服务名称 (location, nlp, gateway)')
    parser.add_argument('--interval', '-i', type=int, default=30, help='监控间隔（秒）')
    
    args = parser.parse_args()
    
    manager = MicroserviceManager()
    
    try:
        if args.action == 'start':
            if args.service:
                success = manager.start_service(args.service)
            else:
                success = manager.start_all_services()
            
            if success and not args.service:
                # 启动监控
                manager.monitor_services()
        
        elif args.action == 'stop':
            if args.service:
                manager.stop_service(args.service)
            else:
                manager.stop_all_services()
        
        elif args.action == 'restart':
            if args.service:
                manager.restart_service(args.service)
            else:
                manager.stop_all_services()
                time.sleep(2)
                manager.start_all_services()
        
        elif args.action == 'status':
            manager._print_service_info()
        
        elif args.action == 'health':
            manager.health_check()
        
        elif args.action == 'monitor':
            manager.monitor_services(args.interval)
    
    except KeyboardInterrupt:
        print("\n正在停止所有服务...")
        manager.stop_all_services()
        print("所有服务已停止")


if __name__ == '__main__':
    main()
