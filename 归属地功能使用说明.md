# 电话号码归属地识别功能使用说明

## 功能概述

本项目已成功集成电话号码归属地识别功能，能够在进行电话标记识别时自动查询并添加省份、城市、运营商等归属地信息。

## 主要特性

### ✅ 已实现功能

1. **智能归属地查询**
   - 手机号码：基于前7位号段精确匹配
   - 固话号码：基于区号（3-4位）智能匹配
   - 自动处理各种号码格式（国际前缀、区号前缀等）

2. **高性能数据库**
   - 手机号段数据：517,574条记录，覆盖31个省份
   - 固话区号数据：321条记录，覆盖全国主要城市
   - 平均查询时间：<1ms，查询成功率：93%+

3. **智能缓存机制**
   - 自动缓存查询结果，提高重复查询效率
   - 支持缓存过期管理（默认30天）

4. **完整输出格式**
   - Excel/CSV导出包含完整归属地信息
   - 字段包括：省份、城市、运营商、数据源等

## 使用方法

### 1. 自动集成使用

归属地功能已自动集成到主程序中，无需额外配置：

```bash
python3 ml_kit_phone_marker.py
```

在进行电话标记识别时，系统会自动：
1. 查询号码归属地信息
2. 将结果保存到数据库
3. 在导出文件中包含归属地字段

### 2. 独立使用归属地查询

```python
from location_manager import LocationManager

# 创建归属地管理器
lm = LocationManager()

# 查询手机号归属地
result = lm.get_phone_location('13800138000')
print(f"归属地: {result['province']} {result['city']} {result['isp']}")

# 查询固话归属地
result = lm.get_phone_location('02088888888')
print(f"归属地: {result['province']} {result['city']}")
```

### 3. 批量查询示例

```python
from location_manager import LocationManager

lm = LocationManager()
phone_numbers = ['13800138000', '02088888888', '057188888888']

for phone in phone_numbers:
    result = lm.get_phone_location(phone)
    print(f"{phone}: {result['province']} {result['city']} {result.get('isp', '')}")
```

## 输出格式说明

### Excel/CSV导出字段

| 字段名 | 说明 | 示例 |
|--------|------|------|
| phone_number | 电话号码 | 13800138000 |
| mark_info | 标记信息 | 骚扰电话 |
| province | 省份 | 北京 |
| city | 城市 | 北京 |
| isp | 运营商 | 中国移动 |
| location_source | 数据源 | database/ocr/none |
| phone_model | 手机型号 | huawei MED-AL00 |
| android_version | 安卓版本 | 10 |
| status | 处理状态 | success |
| processing_time_formatted | 处理时间 | 2.50秒 |
| method | 识别方法 | ML Kit |
| dial_time | 拨号时间 | 2025-06-30 18:00:00 |

### 数据源说明

- `database`: 从归属地数据库查询获得（推荐，准确度高）
- `ocr`: 从OCR识别结果提取（备用方案）
- `none`: 未找到归属地信息

## 性能指标

### 查询性能
- **平均查询时间**: <1ms
- **查询成功率**: 93%+
- **数据库大小**: ~50MB
- **内存占用**: 低

### 支持的号码类型
- ✅ 11位手机号码（如：13800138000）
- ✅ 固话号码（如：02088888888）
- ✅ 国际格式（如：+8613800138000）
- ✅ 各种格式变体（带空格、连字符等）
- ❌ 400/800号码（不支持归属地查询）
- ❌ 短号码（如：10086、95588）

## 数据维护

### 数据更新

如需更新归属地数据，请：

1. 替换CSV文件：
   - `号段-phone-202506-517574/qqzeng-phone-202506-517574.csv`（手机号段）
   - `号段-phone-202506-517574/副本城市号码.csv`（固话区号）

2. 重新导入数据：
```bash
python3 import_location_data.py
```

### 缓存管理

清理过期缓存：
```python
from location_manager import LocationManager
lm = LocationManager()
deleted_count = lm.clean_expired_cache(days=30)
print(f"清理了 {deleted_count} 条过期缓存")
```

### 数据库升级

如需升级数据库结构：
```bash
python3 upgrade_database.py
```

## 故障排除

### 常见问题

1. **查询结果为空**
   - 检查号码格式是否正确
   - 确认号码是否在数据库覆盖范围内
   - 查看日志了解具体错误信息

2. **查询速度慢**
   - 检查数据库索引是否正常
   - 清理过期缓存
   - 重启程序重新初始化

3. **导出文件缺少归属地字段**
   - 确认数据库已升级到最新结构
   - 检查程序是否正确集成LocationManager

### 测试工具

运行完整测试：
```bash
python3 final_validation_test.py
```

运行性能测试：
```bash
python3 test_complete_functionality.py
```

## 技术架构

### 数据库设计
- `mobile_location`: 手机号段归属地表
- `landline_location`: 固话区号归属地表  
- `location_cache`: 查询结果缓存表

### 查询优化
- 基于segment字段的B-tree索引
- 智能缓存机制
- 批量查询优化

### 集成方式
- 优先使用数据库查询
- 备用OCR识别结果
- 自动数据源标记

## 更新日志

### v1.0.0 (2025-06-30)
- ✅ 完成归属地数据库设计和实现
- ✅ 集成到主程序ml_kit_phone_marker.py
- ✅ 支持Excel/CSV导出归属地信息
- ✅ 实现高性能查询和缓存机制
- ✅ 通过全面功能测试验证
- ✅ 优化导出格式，移除冗余的location字段

## 输出示例

现在当您使用系统识别电话号码时，输出的Excel表格将包含：

| 电话号码 | 标记信息 | 省份 | 城市 | 运营商 | 数据源 |
|----------|----------|------|------|--------|--------|
| 13800138000 | 骚扰电话 | 北京 | 北京 | 中国移动 | database |
| 02088888888 | 推销电话 | 广东 | 广州 |  | database |

**注意**: 为避免信息冗余，已移除location字段，省份和城市信息已足够表示归属地。

---

## 联系支持

如有问题或建议，请查看项目日志文件或运行测试脚本进行诊断。
