import subprocess
import time
import json
import sqlite3
import pandas as pd
import re
from datetime import datetime
from typing import List, Dict, Optional
import os
import cv2
import numpy as np
from PIL import Image
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phone_marker.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class Config:
    """配置管理类"""
    def __init__(self):
        self.config_file = 'config.json'
        self.default_config = {
            'wait_time': 2.0,  # 界面加载等待时间
            'interval': 3,     # 批量处理间隔
            'max_retries': 3,  # 最大重试次数
            'ocr_configs': [
                r'--oem 3 --psm 6 -l chi_sim+eng',
                r'--oem 3 --psm 8 -l chi_sim+eng',
                r'--oem 3 --psm 13 -l chi_sim+eng'
            ],
            'keywords': [
                "标记", "广告", "人标记", "人举报", "推销", "诈骗", "举报", "送餐", "快递",
                "骚扰", "中介", "保险", "贷款", "投资", "理财", "教育", "培训",
                "房产", "装修", "医疗", "保健", "美容", "健身", "旅游", "酒店", "餐饮",
                "电商", "物流", "客服", "售后", "推广", "营销", "销售", "业务", "服务",
                "人", "次", "个", "条", "个标记", "次举报", "人举报", "人标记过"
            ],
            'auto_export': True,  # 自动导出
            'export_format': 'excel',  # 导出格式
            'max_workers': 1,  # 并发处理数量（建议保持1，避免冲突）
            'screenshot_quality': 0.8,  # 截图质量
            'enable_logging': True  # 启用日志
        }
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = {**self.default_config, **json.load(f)}
            else:
                self.config = self.default_config.copy()
                self.save_config()
        except Exception as e:
            logging.warning(f"加载配置文件失败: {e}")
            self.config = self.default_config.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def get(self, key, default=None):
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置值"""
        self.config[key] = value
        self.save_config()

class MLKitPhoneMarker:
    """基于ML Kit的电话标记识别系统"""
    
    def __init__(self):
        self.config = Config()
        self.device_id = self._select_device()
        self.device_connected = self.device_id is not None
        self.db_manager = DatabaseManager()
        self.keywords = self.config.get('keywords', [])
        self.retry_count = 0
        self.max_retries = self.config.get('max_retries', 3)

        # 初始化归属地管理器
        try:
            from location_manager import LocationManager
            self.location_manager = LocationManager()
            logging.info("归属地查询功能已启用")
        except ImportError as e:
            logging.warning(f"归属地查询功能不可用: {e}")
            self.location_manager = None
        
        # 添加归属地关键词
        self.location_keywords = [
            "北京", "上海", "广州", "深圳", "天津", "重庆", "成都", "杭州", "南京", "武汉",
            "西安", "郑州", "长沙", "青岛", "沈阳", "大连", "济南", "哈尔滨", "长春", "石家庄",
            "太原", "呼和浩特", "合肥", "福州", "厦门", "南昌", "南宁", "海口", "贵阳", "昆明",
            "拉萨", "兰州", "西宁", "银川", "乌鲁木齐", "台湾", "香港", "澳门",
            "广东", "江苏", "浙江", "山东", "河南", "四川", "湖北", "湖南", "河北", "福建",
            "云南", "江西", "安徽", "山西", "广西", "贵州", "辽宁", "吉林", "黑龙江", "陕西",
            "甘肃", "青海", "内蒙古", "新疆", "西藏", "宁夏", "海南", "台湾", "香港", "澳门"
        ]
        # 城市到省份映射
        self.city_to_province = {
            "厦门": "福建", "福州": "福建", "泉州": "福建", "漳州": "福建", "三明": "福建", "莆田": "福建", "南平": "福建", "龙岩": "福建", "宁德": "福建",
            "广州": "广东", "深圳": "广东", "珠海": "广东", "汕头": "广东", "佛山": "广东", "韶关": "广东", "湛江": "广东", "肇庆": "广东", "江门": "广东", "茂名": "广东", "惠州": "广东", "梅州": "广东", "汕尾": "广东", "河源": "广东", "阳江": "广东", "清远": "广东", "东莞": "广东", "中山": "广东", "潮州": "广东", "揭阳": "广东", "云浮": "广东",
            "南京": "江苏", "苏州": "江苏", "无锡": "江苏", "常州": "江苏", "南通": "江苏", "连云港": "江苏", "淮安": "江苏", "盐城": "江苏", "扬州": "江苏", "镇江": "江苏", "泰州": "江苏", "宿迁": "江苏",
            "杭州": "浙江", "宁波": "浙江", "温州": "浙江", "嘉兴": "浙江", "湖州": "浙江", "绍兴": "浙江", "金华": "浙江", "衢州": "浙江", "舟山": "浙江", "台州": "浙江", "丽水": "浙江",
            "济南": "山东", "青岛": "山东", "淄博": "山东", "枣庄": "山东", "东营": "山东", "烟台": "山东", "潍坊": "山东", "济宁": "山东", "泰安": "山东", "威海": "山东", "日照": "山东", "莱芜": "山东", "临沂": "山东", "德州": "山东", "聊城": "山东", "滨州": "山东", "菏泽": "山东",
            # ... 可继续补充全国主要城市
        }
        
        if self.device_connected:
            self.phone_info = self._get_phone_info()
            logging.info(f"检测到手机: {self.phone_info}")
        else:
            logging.error("设备未连接")

    def _select_device(self):
        """检测并选择设备，返回设备ID"""
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
        devices = result.stdout.strip().split('\n')[1:]
        device_ids = [d.split('\t')[0] for d in devices if d.strip() and 'device' in d]
        if not device_ids:
            print("未检测到可用设备，请检查ADB连接和USB调试")
            return None
        if len(device_ids) == 1:
            print(f"检测到设备: {device_ids[0]}")
            return device_ids[0]
        print("检测到多台设备：")
        for idx, dev in enumerate(device_ids):
            print(f"{idx+1}. {dev}")
        sel = input(f"请选择要操作的设备 (1-{len(device_ids)}): ").strip()
        try:
            sel_idx = int(sel) - 1
            if 0 <= sel_idx < len(device_ids):
                return device_ids[sel_idx]
        except Exception:
            pass
        print("无效选择，默认选择第一个设备")
        return device_ids[0]

    def _get_phone_info(self) -> Dict:
        brand = subprocess.run(['adb', 'shell', 'getprop', 'ro.product.brand'], capture_output=True, text=True).stdout.strip().lower()
        model = subprocess.run(['adb', 'shell', 'getprop', 'ro.product.model'], capture_output=True, text=True).stdout.strip()
        res_out = subprocess.run(['adb', 'shell', 'wm', 'size'], capture_output=True, text=True).stdout
        match = re.search(r'(\d+)x(\d+)', res_out)
        if match:
            resolution = [int(match.group(1)), int(match.group(2))]
        else:
            resolution = [1080, 2400]
        android_version = subprocess.run(['adb', 'shell', 'getprop', 'ro.build.version.release'], capture_output=True, text=True).stdout.strip()
        return {'brand': brand, 'model': model, 'resolution': resolution, 'android_version': android_version}

    def _run_adb(self, cmd: list, **kwargs):
        """带设备ID的ADB命令，自动打印命令和返回内容"""
        full_cmd = ['adb', '-s', self.device_id] + cmd
        print(f'执行命令: {" ".join(full_cmd)}')
        result = subprocess.run(full_cmd, capture_output=True, text=True, **kwargs)
        print(f'命令返回: {result.stdout.strip()} {result.stderr.strip()}')
        return result

    def _dial_number(self, phone_number: str):
        """拨号，兼容vivo，先CALL失败再DIAL"""
        # 先CALL
        result = self._run_adb(['shell', 'am', 'start', '-a', 'android.intent.action.CALL', '-d', f'tel:{phone_number}'])
        if 'Error' in result.stdout or 'Exception' in result.stdout or result.returncode != 0:
            print('CALL方式失败，尝试DIAL方式...')
            result = self._run_adb(['shell', 'am', 'start', '-a', 'android.intent.action.DIAL', '-d', f'tel:{phone_number}'])
        return result

    def take_screenshot(self) -> str:
        """截图并保存到本地"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        screenshot_path = f"screenshots/screenshot_{timestamp}.png"
        os.makedirs('screenshots', exist_ok=True)
        self._run_adb(['shell', 'screencap', '-p', '/sdcard/screenshot.png'])
        self._run_adb(['pull', '/sdcard/screenshot.png', screenshot_path])
        print(f"截图已保存: {screenshot_path}")
        return screenshot_path

    def analyze_image_with_ml_kit(self, image_path: str) -> List[Dict]:
        """使用ML Kit分析图像中的文本"""
        try:
            import pytesseract
            
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                logging.error(f"无法读取图像: {image_path}")
                return []
            
            # 图像预处理优化
            processed_images = self._preprocess_images(image)
            
            all_results = []
            all_text = ""  # 收集所有文本用于归属地识别
            ocr_configs = self.config.get('ocr_configs', [
                r'--oem 3 --psm 6 -l chi_sim+eng',
                r'--oem 3 --psm 8 -l chi_sim+eng',
                r'--oem 3 --psm 13 -l chi_sim+eng'
            ])
            
            # 对每种预处理方法进行OCR
            for i, processed_image in enumerate(processed_images):
                try:
                    for j, config in enumerate(ocr_configs):
                        text = pytesseract.image_to_string(processed_image, config=config)
                        all_text += text + "\n"  # 收集所有文本
                        
                        # 分析文本
                        results = self._analyze_text(text, i, j)
                        all_results.extend(results)
                
                except Exception as e:
                    logging.warning(f"OCR方法{i}失败: {e}")
                    continue
            
            # 智能去重和排序
            unique_results = self._deduplicate_results(all_results)
            
            # 添加归属地识别结果
            location_info = self._extract_location_info(all_text)
            if location_info['location']:
                unique_results.append({
                    'text': f"归属地: {location_info['location']}",
                    'keyword': '归属地',
                    'confidence': 0.9,
                    'method': 'Location_Detection',
                    'location_info': location_info
                })
            
            return unique_results
            
        except Exception as e:
            logging.error(f"ML Kit分析失败: {e}")
            return []
    
    def _preprocess_images(self, image):
        """图像预处理优化"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 多种预处理方法
        processed_images = [
            gray,  # 原始灰度图
            cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1],  # 二值化
            cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2),  # 自适应阈值
            cv2.fastNlMeansDenoising(gray),  # 降噪
            cv2.GaussianBlur(gray, (3, 3), 0),  # 高斯模糊
            cv2.medianBlur(gray, 3),  # 中值滤波
            # 新增：形态学操作
            cv2.morphologyEx(gray, cv2.MORPH_CLOSE, np.ones((3,3), np.uint8)),
            cv2.morphologyEx(gray, cv2.MORPH_OPEN, np.ones((3,3), np.uint8))
        ]
        
        return processed_images
    
    def _analyze_text(self, text: str, method_idx: int, config_idx: int) -> List[Dict]:
        """分析文本内容"""
        results = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or len(line) < 2:
                continue
            
            # 检查关键词
            for keyword in self.keywords:
                if keyword in line:
                    confidence = 0.7 + (method_idx * 0.05) + (config_idx * 0.02)
                    results.append({
                        'text': line,
                        'keyword': keyword,
                        'confidence': confidence,
                        'method': f'OCR_{method_idx}_{config_idx}'
                    })
                    break
        
        return results
    
    def _deduplicate_results(self, all_results: List[Dict]) -> List[Dict]:
        """智能去重和排序"""
        # 按置信度排序
        all_results.sort(key=lambda x: x['confidence'], reverse=True)
        
        unique_results = []
        seen_texts = set()
        
        for result in all_results:
            # 智能去重：检查文本相似度
            text = result['text']
            is_duplicate = False
            
            for seen_text in seen_texts:
                if self._text_similarity(text, seen_text) > 0.8:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_results.append(result)
                seen_texts.add(text)
        
        return unique_results
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 简单的字符重叠率计算
        set1 = set(text1)
        set2 = set(text2)
        
        if not set1 or not set2:
            return 0.0
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0

    def detect_mark_with_ml_kit(self, phone_number: str) -> Dict:
        """使用ML Kit检测号码标记"""
        if not self.device_connected:
            return {'status': 'failed', 'error': '设备未连接'}
        
        start_time = time.time()
        retry_count = 0
        
        while retry_count <= self.max_retries:
            try:
                logging.info(f"正在使用ML Kit分析号码: {phone_number} (尝试 {retry_count + 1}/{self.max_retries + 1})")
                
                # 启动拨号界面
                self._dial_number(phone_number)
                
                # 等待界面加载
                wait_time = self.config.get('wait_time', 2.0)
                logging.info(f"等待界面加载 {wait_time} 秒...")
                time.sleep(wait_time)
                
                # 截图
                screenshot_path = self.take_screenshot()
                
                # 立即挂断，避免打扰对方
                logging.info("截图完成，立即挂断，避免打扰对方...")
                self.auto_hangup()
                
                # 使用ML Kit分析图像
                logging.info("使用ML Kit分析图像...")
                ml_results = self.analyze_image_with_ml_kit(screenshot_path)
                
                # 处理结果
                mark_info = self._process_ml_results(ml_results)
                
                # 提取归属地信息 - 优先使用数据库查询，其次使用OCR识别
                location_info = self._get_comprehensive_location_info(phone_number, ml_results)

                processing_time = time.time() - start_time
                record = {
                    'phone_number': phone_number,
                    'mark_info': mark_info,
                    'province': location_info.get('province', ''),
                    'city': location_info.get('city', ''),
                    'location': location_info.get('location', ''),
                    'isp': location_info.get('isp', ''),  # 运营商信息
                    'location_source': location_info.get('source', ''),  # 归属地数据源
                    'phone_model': f"{self.phone_info['brand']} {self.phone_info['model']}",
                    'android_version': self.phone_info['android_version'],
                    'status': 'success',
                    'screenshot_path': screenshot_path,
                    'processing_time': processing_time,
                    'method': 'ML Kit',
                    'retry_count': retry_count
                }
                
                self.db_manager.save_dial_record(record)
                logging.info(f"处理完成: {phone_number} - {mark_info} (耗时: {processing_time:.2f}秒)")
                return record
                
            except Exception as e:
                retry_count += 1
                logging.error(f"ML Kit检测失败 (尝试 {retry_count}/{self.max_retries + 1}): {e}")
                
                # 尝试挂断
                try:
                    self.auto_hangup()
                except:
                    pass
                
                if retry_count <= self.max_retries:
                    logging.info(f"等待 2 秒后重试...")
                    time.sleep(2)
                else:
                    # 最后一次尝试失败
                    processing_time = time.time() - start_time
                    record = {
                        'phone_number': phone_number,
                        'mark_info': f'ML Kit检测失败: {str(e)[:50]}',
                        'phone_model': f"{self.phone_info['brand']} {self.phone_info['model']}" if self.phone_info else 'Unknown',
                        'android_version': self.phone_info['android_version'] if self.phone_info else 'Unknown',
                        'status': 'failed',
                        'processing_time': processing_time,
                        'retry_count': retry_count
                    }
                    self.db_manager.save_dial_record(record)
                    return record
    
    def _process_ml_results(self, ml_results: List[Dict]) -> str:
        """处理ML Kit分析结果"""
        if not ml_results:
            return "未检测到标记信息"
        
        # 获取最高置信度的结果
        best_result = ml_results[0]
        mark_info = best_result['text']
        
        # 记录检测到的信息
        logging.info(f"ML Kit检测到标记信息: {mark_info} (置信度: {best_result['confidence']:.2f})")
        
        # 如果有多个结果，记录所有检测到的信息
        if len(ml_results) > 1:
            logging.info(f"还检测到其他信息: {[r['text'] for r in ml_results[1:3]]}")
        
        return mark_info

    def auto_hangup(self):
        """自动挂断"""
        logging.info("开始自动挂断...")
        for i in range(3):
            self._run_adb(['shell', 'input', 'keyevent', 'KEYCODE_ENDCALL'])
            time.sleep(0.3)
        self._run_adb(['shell', 'service', 'call', 'phone', '5'])
        time.sleep(0.5)
        logging.info("自动挂断完成")

    def batch_detect(self, phone_list: List[str], interval: int = None) -> List[Dict]:
        """批量检测号码标记"""
        if interval is None:
            interval = self.config.get('interval', 3)
        
        logging.info(f"开始批量ML Kit检测，共 {len(phone_list)} 个号码")
        results = []
        
        # 性能监控
        start_time = time.time()
        success_count = 0
        failed_count = 0
        
        for i, phone in enumerate(phone_list, 1):
            logging.info(f"\n进度: {i}/{len(phone_list)}")
            result = self.detect_mark_with_ml_kit(phone)
            results.append(result)
            
            # 统计成功/失败
            if result.get('status') == 'success':
                success_count += 1
            else:
                failed_count += 1
            
            if i < len(phone_list):
                logging.info(f"等待 {interval} 秒...")
                time.sleep(interval)
        
        # 批量处理完成统计
        total_time = time.time() - start_time
        logging.info(f"\n批量处理完成！")
        logging.info(f"总耗时: {total_time:.2f}秒")
        logging.info(f"成功: {success_count}个")
        logging.info(f"失败: {failed_count}个")
        logging.info(f"成功率: {success_count/len(phone_list)*100:.1f}%")
        
        # 自动导出结果
        if self.config.get('auto_export', True):
            logging.info("正在自动导出结果...")
            export_format = self.config.get('export_format', 'excel')
            export_filename = self.export_results(export_format)
            if export_filename:
                logging.info(f"✅ 结果已自动导出到: {export_filename}")
            else:
                logging.error("❌ 自动导出失败，请手动选择选项3导出")
        
        return results

    def smart_batch_detect(self, phone_list: List[str]) -> List[Dict]:
        """智能批量检测 - 根据成功率动态调整参数"""
        logging.info("启用智能批量检测模式")
        
        # 动态调整参数
        original_wait_time = self.config.get('wait_time', 2.0)
        original_interval = self.config.get('interval', 3)
        
        results = []
        consecutive_failures = 0
        max_consecutive_failures = 3
        
        for i, phone in enumerate(phone_list, 1):
            logging.info(f"\n智能检测进度: {i}/{len(phone_list)}")
            
            # 动态调整等待时间
            if consecutive_failures > 0:
                adjusted_wait_time = min(original_wait_time + consecutive_failures * 0.5, 5.0)
                self.config.set('wait_time', adjusted_wait_time)
                logging.info(f"检测到连续失败，调整等待时间为 {adjusted_wait_time} 秒")
            
            result = self.detect_mark_with_ml_kit(phone)
            results.append(result)
            
            # 更新连续失败计数
            if result.get('status') == 'success':
                consecutive_failures = 0
                # 成功时恢复原始参数
                self.config.set('wait_time', original_wait_time)
            else:
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    logging.warning(f"连续失败 {consecutive_failures} 次，建议检查设备状态")
            
            # 动态调整间隔
            if i < len(phone_list):
                interval = original_interval + (consecutive_failures * 1)
                logging.info(f"等待 {interval} 秒...")
                time.sleep(interval)
        
        # 恢复原始配置
        self.config.set('wait_time', original_wait_time)
        
        return results

    def show_config_menu(self):
        """显示配置管理菜单"""
        while True:
            print("\n=== 配置管理 ===")
            print("1. 查看当前配置")
            print("2. 修改等待时间")
            print("3. 修改检测间隔")
            print("4. 修改最大重试次数")
            print("5. 修改关键词列表")
            print("6. 修改自动导出设置")
            print("7. 重置为默认配置")
            print("8. 返回主菜单")
            
            choice = input("\n请选择功能 (1-8): ").strip()
            
            if choice == '1':
                self._show_current_config()
            elif choice == '2':
                self._modify_wait_time()
            elif choice == '3':
                self._modify_interval()
            elif choice == '4':
                self._modify_max_retries()
            elif choice == '5':
                self._modify_keywords()
            elif choice == '6':
                self._modify_auto_export()
            elif choice == '7':
                self._reset_config()
            elif choice == '8':
                break
            else:
                print("无效选择")

    def _show_current_config(self):
        """显示当前配置"""
        print("\n当前配置:")
        print(f"等待时间: {self.config.get('wait_time')} 秒")
        print(f"检测间隔: {self.config.get('interval')} 秒")
        print(f"最大重试次数: {self.config.get('max_retries')}")
        print(f"自动导出: {'是' if self.config.get('auto_export') else '否'}")
        print(f"导出格式: {self.config.get('export_format')}")
        print(f"关键词数量: {len(self.config.get('keywords', []))}")

    def _modify_wait_time(self):
        """修改等待时间"""
        current = self.config.get('wait_time', 2.0)
        new_time = input(f"当前等待时间: {current}秒，请输入新的等待时间 (1.0-10.0): ").strip()
        try:
            new_time = float(new_time)
            if 1.0 <= new_time <= 10.0:
                self.config.set('wait_time', new_time)
                print(f"等待时间已修改为: {new_time}秒")
            else:
                print("等待时间必须在1.0-10.0秒之间")
        except ValueError:
            print("请输入有效的数字")

    def _modify_interval(self):
        """修改检测间隔"""
        current = self.config.get('interval', 3)
        new_interval = input(f"当前检测间隔: {current}秒，请输入新的间隔 (1-30): ").strip()
        try:
            new_interval = int(new_interval)
            if 1 <= new_interval <= 30:
                self.config.set('interval', new_interval)
                print(f"检测间隔已修改为: {new_interval}秒")
            else:
                print("检测间隔必须在1-30秒之间")
        except ValueError:
            print("请输入有效的数字")

    def _modify_max_retries(self):
        """修改最大重试次数"""
        current = self.config.get('max_retries', 3)
        new_retries = input(f"当前最大重试次数: {current}，请输入新的重试次数 (0-5): ").strip()
        try:
            new_retries = int(new_retries)
            if 0 <= new_retries <= 5:
                self.config.set('max_retries', new_retries)
                self.max_retries = new_retries
                print(f"最大重试次数已修改为: {new_retries}")
            else:
                print("重试次数必须在0-5之间")
        except ValueError:
            print("请输入有效的数字")

    def _modify_keywords(self):
        """修改关键词列表"""
        current_keywords = self.config.get('keywords', [])
        print(f"当前关键词数量: {len(current_keywords)}")
        print("前10个关键词:", current_keywords[:10])
        
        choice = input("1. 添加关键词 2. 删除关键词 3. 重置关键词: ").strip()
        
        if choice == '1':
            new_keyword = input("请输入要添加的关键词: ").strip()
            if new_keyword and new_keyword not in current_keywords:
                current_keywords.append(new_keyword)
                self.config.set('keywords', current_keywords)
                self.keywords = current_keywords
                print(f"已添加关键词: {new_keyword}")
            else:
                print("关键词已存在或为空")
        elif choice == '2':
            if current_keywords:
                print("当前关键词:", current_keywords)
                keyword_to_remove = input("请输入要删除的关键词: ").strip()
                if keyword_to_remove in current_keywords:
                    current_keywords.remove(keyword_to_remove)
                    self.config.set('keywords', current_keywords)
                    self.keywords = current_keywords
                    print(f"已删除关键词: {keyword_to_remove}")
                else:
                    print("关键词不存在")
        elif choice == '3':
            self.config.set('keywords', self.config.default_config['keywords'])
            self.keywords = self.config.get('keywords')
            print("关键词已重置为默认值")

    def _modify_auto_export(self):
        """修改自动导出设置"""
        current = self.config.get('auto_export', True)
        print(f"当前自动导出: {'是' if current else '否'}")
        
        choice = input("是否启用自动导出? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            self.config.set('auto_export', True)
            print("已启用自动导出")
        elif choice in ['n', 'no', '否']:
            self.config.set('auto_export', False)
            print("已禁用自动导出")
        else:
            print("无效选择")

    def _reset_config(self):
        """重置为默认配置"""
        choice = input("确定要重置为默认配置吗? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            self.config.config = self.config.default_config.copy()
            self.config.save_config()
            self.keywords = self.config.get('keywords')
            self.max_retries = self.config.get('max_retries')
            print("配置已重置为默认值")

    def export_results(self, format: str = 'excel') -> str:
        """导出结果到Excel或CSV文件"""
        try:
            conn = sqlite3.connect(self.db_manager.db_path)
            
            # 查询所有记录，按时间倒序排列
            query = '''
                SELECT 
                    phone_number,
                    mark_info,
                    province,
                    city,
                    location,
                    phone_model,
                    android_version,
                    status,
                    processing_time,
                    method,
                    dial_time
                FROM dial_records 
                ORDER BY dial_time DESC
            '''
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                print("没有找到记录，无法导出")
                return ""
            
            # 格式化处理时间
            df['processing_time_formatted'] = df['processing_time'].apply(
                lambda x: f"{x:.2f}秒" if pd.notna(x) and x > 0 else "N/A"
            )
            
            # 去重：保留每个号码的最新记录
            df_unique = df.drop_duplicates(subset=['phone_number'], keep='first')
            
            # 重新排序列
            export_columns = [
                'phone_number',
                'mark_info',
                'province',
                'city',
                'isp',  # 运营商
                'location_source',  # 归属地数据源
                'phone_model',
                'android_version',
                'status',
                'processing_time_formatted',
                'method',
                'dial_time'
            ]

            # 检查DataFrame中是否包含新字段，如果不包含则添加空列
            for col in ['isp', 'location_source']:
                if col not in df_unique.columns:
                    df_unique[col] = ''
            
            df_export = df_unique[export_columns].copy()
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if format.lower() == 'excel':
                filename = f"phone_mark_results_{timestamp}.xlsx"
                df_export.to_excel(filename, index=False, engine='openpyxl')
                print(f"结果已导出到Excel文件: {filename}")
            else:
                filename = f"phone_mark_results_{timestamp}.csv"
                df_export.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"结果已导出到CSV文件: {filename}")
            
            # 显示统计信息
            print(f"\n导出统计:")
            print(f"原始记录数: {len(df)}")
            print(f"去重后记录数: {len(df_export)}")
            print(f"成功识别: {len(df_export[df_export['status'] == 'success'])}")
            print(f"识别失败: {len(df_export[df_export['status'] == 'failed'])}")
            
            # 显示标记信息分布
            mark_counts = df_export['mark_info'].value_counts()
            print(f"\n标记信息分布:")
            for mark, count in mark_counts.items():
                print(f"  {mark}: {count}次")
            
            return filename
            
        except Exception as e:
            print(f"导出失败: {e}")
            return ""

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        try:
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()
            
            # 总记录数
            cursor.execute('SELECT COUNT(*) FROM dial_records')
            total_records = cursor.fetchone()[0]
            
            # 去重后的记录数（每个号码的最新记录）
            cursor.execute('''
                SELECT COUNT(DISTINCT phone_number) FROM dial_records
            ''')
            unique_records = cursor.fetchone()[0]
            
            # 成功记录数
            cursor.execute('SELECT COUNT(*) FROM dial_records WHERE status = "success"')
            success_records = cursor.fetchone()[0]
            
            # 手机型号分布
            cursor.execute('''
                SELECT phone_model, COUNT(*) as count 
                FROM dial_records 
                GROUP BY phone_model 
                ORDER BY count DESC
            ''')
            model_distribution = dict(cursor.fetchall())
            
            # 标记类型统计（去重后）
            cursor.execute('''
                SELECT mark_info, COUNT(*) as count 
                FROM (
                    SELECT phone_number, mark_info
                    FROM dial_records 
                    WHERE mark_info IS NOT NULL AND mark_info != '' 
                    AND mark_info != '未检测到标记信息'
                    AND mark_info != '标记信息查询中（需要手动确认）'
                    GROUP BY phone_number
                    ORDER BY dial_time DESC
                )
                GROUP BY mark_info 
                ORDER BY count DESC 
                LIMIT 10
            ''')
            mark_distribution = dict(cursor.fetchall())
            
            # 归属地统计
            cursor.execute('''
                SELECT province, COUNT(*) as count 
                FROM (
                    SELECT phone_number, province
                    FROM dial_records 
                    WHERE province IS NOT NULL AND province != ''
                    GROUP BY phone_number
                    ORDER BY dial_time DESC
                )
                GROUP BY province 
                ORDER BY count DESC 
                LIMIT 10
            ''')
            province_distribution = dict(cursor.fetchall())
            
            # 城市统计
            cursor.execute('''
                SELECT city, COUNT(*) as count 
                FROM (
                    SELECT phone_number, city
                    FROM dial_records 
                    WHERE city IS NOT NULL AND city != ''
                    GROUP BY phone_number
                    ORDER BY dial_time DESC
                )
                GROUP BY city 
                ORDER BY count DESC 
                LIMIT 10
            ''')
            city_distribution = dict(cursor.fetchall())
            
            # 平均处理时间
            cursor.execute('SELECT AVG(processing_time) FROM dial_records WHERE processing_time > 0')
            avg_time = cursor.fetchone()[0] or 0
            
            conn.close()
            
            return {
                'total_records': total_records,
                'unique_records': unique_records,
                'success_records': success_records,
                'success_rate': (success_records / total_records * 100) if total_records > 0 else 0,
                'avg_processing_time': round(avg_time, 2),
                'model_distribution': model_distribution,
                'mark_distribution': mark_distribution,
                'province_distribution': province_distribution,
                'city_distribution': city_distribution
            }
            
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}

    def _extract_location_info(self, text: str) -> Dict:
        """从文本中提取归属地信息"""
        location_info = {
            'province': '',
            'city': '',
            'location': ''
        }
        
        # 分行处理文本
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or len(line) < 2:
                continue
            
            # 检查是否包含归属地信息
            for location in self.location_keywords:
                if location in line:
                    # 尝试提取完整的归属地信息
                    location_text = self._extract_location_from_line(line, location)
                    if location_text:
                        location_info['location'] = location_text
                        
                        # 分离省份和城市
                        province, city = self._separate_province_city(location_text)
                        location_info['province'] = province
                        location_info['city'] = city
                        
                        logging.info(f"检测到归属地: {location_text}")
                        return location_info
        
        return location_info
    
    def _extract_location_from_line(self, line: str, location: str) -> str:
        """从行中提取完整的归属地信息"""
        # 常见的归属地显示模式
        patterns = [
            rf"({location}[^\s]*?[省市区县])",  # 北京、广东等
            rf"({location}[^\s]*?[移动联通电信])",  # 北京移动、广东联通等
            rf"({location}[^\s]*?[网])",  # 北京网、广东网等
            rf"({location})",  # 单独的地名
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                return match.group(1)
        
        return location
    
    def _separate_province_city(self, location_text: str) -> tuple:
        """分离省份和城市，优先用城市-省份映射补全省份"""
        province = ''
        city = ''
        
        # 直辖市
        municipalities = ['北京', '上海', '天津', '重庆']
        if location_text in municipalities:
            province = location_text
            city = location_text
            return province, city
        
        # 特别行政区
        special_regions = ['香港', '澳门', '台湾']
        if location_text in special_regions:
            province = location_text
            city = location_text
            return province, city
        
        # 优先用城市-省份映射
        for city_name, prov in self.city_to_province.items():
            if city_name in location_text:
                city = city_name
                province = prov
                return province, city
        
        # 省份
        provinces = [
            '广东', '江苏', '浙江', '山东', '河南', '四川', '湖北', '湖南', '河北', '福建',
            '云南', '江西', '安徽', '山西', '广西', '贵州', '辽宁', '吉林', '黑龙江', '陕西',
            '甘肃', '青海', '内蒙古', '新疆', '西藏', '宁夏', '海南'
        ]
        
        # 城市
        cities = [
            '广州', '深圳', '成都', '杭州', '南京', '武汉', '西安', '郑州', '长沙', '青岛',
            '沈阳', '大连', '济南', '哈尔滨', '长春', '石家庄', '太原', '呼和浩特', '合肥',
            '福州', '厦门', '南昌', '南宁', '海口', '贵阳', '昆明', '拉萨', '兰州', '西宁',
            '银川', '乌鲁木齐'
        ]
        
        # 检查是否为省份
        for p in provinces:
            if p in location_text:
                province = p
                # 尝试从剩余文本中提取城市
                remaining = location_text.replace(p, '').strip()
                for c in cities:
                    if c in remaining:
                        city = c
                        break
                if not city and remaining:
                    city = remaining
                break
        
        # 如果没有找到省份，检查是否为城市
        if not province:
            for c in cities:
                if c in location_text:
                    city = c
                    # 尝试从剩余文本中提取省份
                    remaining = location_text.replace(c, '').strip()
                    for p in provinces:
                        if p in remaining:
                            province = p
                            break
                    break
        
        return province, city

    def _get_comprehensive_location_info(self, phone_number: str, ml_results: List[Dict]) -> Dict:
        """
        获取综合归属地信息
        优先级：数据库查询 > OCR识别结果
        """
        location_info = {
            'province': '',
            'city': '',
            'location': ''
        }

        # 1. 优先使用数据库查询归属地
        if self.location_manager:
            try:
                db_location = self.location_manager.get_phone_location(phone_number)
                if db_location.get('province') and db_location.get('city'):
                    location_info = {
                        'province': db_location.get('province', ''),
                        'city': db_location.get('city', ''),
                        'location': db_location.get('location', ''),
                        'isp': db_location.get('isp', ''),
                        'source': 'database'
                    }
                    logging.info(f"数据库查询归属地: {location_info['province']} {location_info['city']} {location_info.get('isp', '')}")
                    return location_info
            except Exception as e:
                logging.warning(f"数据库查询归属地失败: {e}")

        # 2. 如果数据库查询失败，使用OCR识别结果
        ocr_location = self._extract_location_from_results(ml_results)
        if ocr_location.get('province') or ocr_location.get('city'):
            ocr_location['source'] = 'ocr'
            logging.info(f"OCR识别归属地: {ocr_location.get('province', '')} {ocr_location.get('city', '')}")
            return ocr_location

        # 3. 都没有找到归属地信息
        location_info['source'] = 'none'
        return location_info

    def _extract_location_from_results(self, ml_results: List[Dict]) -> Dict:
        """从ML结果中提取归属地信息"""
        location_info = {
            'province': '',
            'city': '',
            'location': ''
        }

        for result in ml_results:
            if result.get('keyword') == '归属地' and 'location_info' in result:
                location_info = result['location_info']
                break

        return location_info

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "phone_marks.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dial_records'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            # 检查是否需要添加新列
            cursor.execute("PRAGMA table_info(dial_records)")
            columns = [column[1] for column in cursor.fetchall()]
            
            # 添加缺失的列
            if 'method' not in columns:
                cursor.execute('ALTER TABLE dial_records ADD COLUMN method TEXT DEFAULT "ML Kit"')
            if 'screenshot_path' not in columns:
                cursor.execute('ALTER TABLE dial_records ADD COLUMN screenshot_path TEXT')
            if 'processing_time' not in columns:
                cursor.execute('ALTER TABLE dial_records ADD COLUMN processing_time REAL')
            if 'province' not in columns:
                cursor.execute('ALTER TABLE dial_records ADD COLUMN province TEXT')
            if 'city' not in columns:
                cursor.execute('ALTER TABLE dial_records ADD COLUMN city TEXT')
            if 'location' not in columns:
                cursor.execute('ALTER TABLE dial_records ADD COLUMN location TEXT')
        else:
            # 创建新表
            cursor.execute('''
                CREATE TABLE dial_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    phone_number TEXT,
                    mark_info TEXT,
                    province TEXT,
                    city TEXT,
                    location TEXT,
                    phone_model TEXT,
                    android_version TEXT,
                    status TEXT,
                    screenshot_path TEXT,
                    processing_time REAL,
                    method TEXT DEFAULT "ML Kit",
                    dial_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
        
        conn.commit()
        conn.close()
    
    def save_dial_record(self, record: Dict) -> bool:
        """保存检测记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表结构是否包含新字段
            cursor.execute("PRAGMA table_info(dial_records)")
            columns = [column[1] for column in cursor.fetchall()]

            # 根据表结构决定插入语句
            if 'isp' in columns and 'location_source' in columns:
                cursor.execute('''
                    INSERT INTO dial_records
                    (phone_number, mark_info, province, city, location, isp, location_source,
                     phone_model, android_version, status, screenshot_path, processing_time, method)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record['phone_number'],
                    record['mark_info'],
                    record.get('province', ''),
                    record.get('city', ''),
                    record.get('location', ''),
                    record.get('isp', ''),
                    record.get('location_source', ''),
                    record['phone_model'],
                    record['android_version'],
                    record['status'],
                    record.get('screenshot_path', ''),
                    record.get('processing_time', 0),
                    record.get('method', 'ML Kit')
                ))
            else:
                # 兼容旧表结构
                cursor.execute('''
                    INSERT INTO dial_records
                    (phone_number, mark_info, province, city, location, phone_model, android_version, status,
                     screenshot_path, processing_time, method)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record['phone_number'],
                    record['mark_info'],
                    record.get('province', ''),
                    record.get('city', ''),
                    record.get('location', ''),
                    record['phone_model'],
                    record['android_version'],
                    record['status'],
                    record.get('screenshot_path', ''),
                    record.get('processing_time', 0),
                    record.get('method', 'ML Kit')
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"保存记录失败: {e}")
            return False

def main():
    marker = MLKitPhoneMarker()
    if not marker.device_connected:
        print("请连接Android设备并开启USB调试")
        return
    
    print("=== 基于ML Kit的电话标记识别系统 ===")
    print("1. 手动输入号码 (单个或批量)")
    print("2. 从文件读取号码 (批量处理)")
    print("3. 智能批量检测 (动态调整参数)")
    print("4. 导出结果 (手动导出)")
    print("5. 获取统计信息")
    print("6. 配置管理")
    print("7. 退出")
    print("\n注意: 批量处理完成后会自动导出结果到Excel文件")
    
    while True:
        choice = input("\n请选择功能 (1-7): ").strip()
        if choice == '1':
            phones_input = input("请输入电话号码列表 (用逗号分隔): ").strip()
            phone_list = [p.strip() for p in phones_input.split(',') if p.strip()]
            if phone_list:
                interval = int(input("请输入检测间隔(秒): ") or "3")
                marker.batch_detect(phone_list, interval)
            else:
                print("没有输入有效号码")
        elif choice == '2':
            file_path = input("请输入文件路径 (支持 .txt, .csv, .xlsx): ").strip()
            if os.path.exists(file_path):
                if file_path.endswith('.txt'):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        phone_list = [line.strip() for line in f if line.strip()]
                elif file_path.endswith('.csv'):
                    df = pd.read_csv(file_path)
                    phone_list = [str(p) for p in df.iloc[:, 0] if str(p).strip()]
                elif file_path.endswith('.xlsx'):
                    df = pd.read_excel(file_path)
                    phone_list = [str(p) for p in df.iloc[:, 0] if str(p).strip()]
                else:
                    print("不支持的文件格式")
                    continue
                if phone_list:
                    interval = int(input("请输入检测间隔(秒): ") or "3")
                    marker.batch_detect(phone_list, interval)
                else:
                    print("没有找到有效的电话号码")
            else:
                print("文件不存在")
        elif choice == '3':
            print("\n=== 智能批量检测 ===")
            print("此模式会根据检测成功率动态调整参数")
            file_path = input("请输入文件路径 (支持 .txt, .csv, .xlsx): ").strip()
            if os.path.exists(file_path):
                if file_path.endswith('.txt'):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        phone_list = [line.strip() for line in f if line.strip()]
                elif file_path.endswith('.csv'):
                    df = pd.read_csv(file_path)
                    phone_list = [str(p) for p in df.iloc[:, 0] if str(p).strip()]
                elif file_path.endswith('.xlsx'):
                    df = pd.read_excel(file_path)
                    phone_list = [str(p) for p in df.iloc[:, 0] if str(p).strip()]
                else:
                    print("不支持的文件格式")
                    continue
                if phone_list:
                    marker.smart_batch_detect(phone_list)
                else:
                    print("没有找到有效的电话号码")
            else:
                print("文件不存在")
        elif choice == '4':
            format = input("请输入导出格式 (excel/csv): ").strip().lower()
            if format not in ['excel', 'csv']:
                format = 'excel'
            filename = marker.export_results(format)
            if filename:
                print(f"导出文件: {filename}")
        elif choice == '5':
            stats = marker.get_statistics()
            if stats:
                print("\n=== 统计信息 ===")
                print(f"总记录数: {stats.get('total_records', 0)}")
                print(f"去重后记录数: {stats.get('unique_records', 0)}")
                print(f"成功识别: {stats.get('success_records', 0)}")
                print(f"成功率: {stats.get('success_rate', 0):.1f}%")
                print(f"平均处理时间: {stats.get('avg_processing_time', 0)}秒")
                
                if stats.get('mark_distribution'):
                    print("\n标记类型分布:")
                    for mark, count in stats['mark_distribution'].items():
                        print(f"  {mark}: {count}次")
                
                if stats.get('province_distribution'):
                    print("\n省份分布:")
                    for province, count in stats['province_distribution'].items():
                        print(f"  {province}: {count}次")
                
                if stats.get('city_distribution'):
                    print("\n城市分布:")
                    for city, count in stats['city_distribution'].items():
                        print(f"  {city}: {count}次")
            else:
                print("没有获取到有效的统计信息")
        elif choice == '6':
            marker.show_config_menu()
        elif choice == '7':
            print("退出程序")
            break
        else:
            print("无效选择")

if __name__ == "__main__":
    main() 