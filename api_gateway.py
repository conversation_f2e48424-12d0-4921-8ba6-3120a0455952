"""
API网关
统一管理所有微服务的入口，提供路由、负载均衡、认证、限流等功能
采用保守的实施策略，确保系统稳定性

设计原则：
1. 统一入口：所有API请求通过网关路由
2. 服务发现：自动发现和管理微服务
3. 容错设计：熔断器和重试机制
4. 性能监控：详细的请求统计和性能指标
5. 向后兼容：保持与原有接口的兼容性
"""

import asyncio
import logging
import time
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import threading

# 尝试导入FastAPI和HTTP客户端
try:
    from fastapi import FastAPI, HTTPException, Request, Response
    from fastapi.responses import JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel, Field
    import uvicorn
    import httpx
    FASTAPI_AVAILABLE = True
    HTTPX_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    HTTPX_AVAILABLE = False
    logging.warning("FastAPI或httpx不可用，API网关功能受限")


class ServiceConfig(BaseModel):
    """微服务配置模型"""
    name: str = Field(..., description="服务名称")
    url: str = Field(..., description="服务URL")
    health_check_path: str = Field("/health", description="健康检查路径")
    timeout: float = Field(30.0, description="请求超时时间")
    max_retries: int = Field(3, description="最大重试次数")
    circuit_breaker_threshold: int = Field(5, description="熔断器阈值")


class GatewayStats(BaseModel):
    """网关统计信息模型"""
    total_requests: int = Field(..., description="总请求数")
    successful_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    avg_response_time: float = Field(..., description="平均响应时间")
    service_stats: Dict[str, Any] = Field(..., description="各服务统计")


class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        """
        初始化熔断器
        
        Args:
            failure_threshold: 失败阈值
            recovery_timeout: 恢复超时时间（秒）
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        self._lock = threading.Lock()
    
    def is_open(self) -> bool:
        """检查熔断器是否开启"""
        with self._lock:
            if self.state == 'OPEN':
                # 检查是否可以进入半开状态
                if (self.last_failure_time and 
                    time.time() - self.last_failure_time > self.recovery_timeout):
                    self.state = 'HALF_OPEN'
                    return False
                return True
            return False
    
    def record_success(self):
        """记录成功"""
        with self._lock:
            self.failure_count = 0
            self.state = 'CLOSED'
    
    def record_failure(self):
        """记录失败"""
        with self._lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'


class ServiceRegistry:
    """服务注册中心"""
    
    def __init__(self):
        """初始化服务注册中心"""
        self.services = {}
        self.circuit_breakers = {}
        self.service_stats = {}
        self._lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
    
    def register_service(self, service_config: ServiceConfig):
        """
        注册服务
        
        Args:
            service_config: 服务配置
        """
        with self._lock:
            self.services[service_config.name] = service_config
            self.circuit_breakers[service_config.name] = CircuitBreaker(
                service_config.circuit_breaker_threshold
            )
            self.service_stats[service_config.name] = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'total_response_time': 0.0,
                'avg_response_time': 0.0,
                'last_health_check': None,
                'is_healthy': True
            }
            
        self.logger.info(f"服务已注册: {service_config.name} -> {service_config.url}")
    
    def get_service(self, service_name: str) -> Optional[ServiceConfig]:
        """
        获取服务配置
        
        Args:
            service_name: 服务名称
            
        Returns:
            服务配置或None
        """
        return self.services.get(service_name)
    
    def is_service_available(self, service_name: str) -> bool:
        """
        检查服务是否可用
        
        Args:
            service_name: 服务名称
            
        Returns:
            是否可用
        """
        if service_name not in self.services:
            return False
        
        circuit_breaker = self.circuit_breakers.get(service_name)
        if circuit_breaker and circuit_breaker.is_open():
            return False
        
        stats = self.service_stats.get(service_name, {})
        return stats.get('is_healthy', True)
    
    def record_request_result(self, service_name: str, success: bool, response_time: float):
        """
        记录请求结果
        
        Args:
            service_name: 服务名称
            success: 是否成功
            response_time: 响应时间
        """
        with self._lock:
            if service_name not in self.service_stats:
                return
            
            stats = self.service_stats[service_name]
            stats['total_requests'] += 1
            stats['total_response_time'] += response_time
            
            if success:
                stats['successful_requests'] += 1
                circuit_breaker = self.circuit_breakers.get(service_name)
                if circuit_breaker:
                    circuit_breaker.record_success()
            else:
                stats['failed_requests'] += 1
                circuit_breaker = self.circuit_breakers.get(service_name)
                if circuit_breaker:
                    circuit_breaker.record_failure()
            
            # 更新平均响应时间
            if stats['total_requests'] > 0:
                stats['avg_response_time'] = stats['total_response_time'] / stats['total_requests']
    
    def get_all_services(self) -> Dict[str, ServiceConfig]:
        """获取所有服务"""
        return self.services.copy()
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return self.service_stats.copy()


class APIGateway:
    """
    API网关
    
    功能特性：
    - 服务注册和发现
    - 请求路由和负载均衡
    - 熔断器和重试机制
    - 请求限流和认证
    - 性能监控和统计
    - 健康检查
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化API网关
        
        Args:
            config: 网关配置字典
        """
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)
        
        # 服务注册中心
        self.service_registry = ServiceRegistry()
        
        # HTTP客户端
        if HTTPX_AVAILABLE:
            self.http_client = httpx.AsyncClient(timeout=30.0)
        else:
            self.http_client = None
        
        # 网关统计
        self.start_time = time.time()
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_response_time = 0.0
        
        # 请求限流
        self.rate_limiter = {}  # 简单的内存限流器
        
        # 初始化默认服务
        self._register_default_services()
        
        # 初始化FastAPI应用（如果可用）
        if FASTAPI_AVAILABLE:
            self.app = self._create_fastapi_app()
        else:
            self.app = None
            self.logger.warning("FastAPI不可用，API网关功能受限")
        
        self.logger.info("API网关初始化完成")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'host': '127.0.0.1',
            'port': 8000,
            'debug': False,
            'enable_cors': True,
            'rate_limit_requests_per_minute': 1000,
            'enable_auth': False,
            'log_level': 'INFO',
            'health_check_interval': 30,  # 健康检查间隔（秒）
            'request_timeout': 30.0
        }
    
    def _register_default_services(self):
        """注册默认的微服务"""
        default_services = [
            ServiceConfig(
                name="location",
                url="http://127.0.0.1:8001",
                health_check_path="/health",
                timeout=30.0,
                max_retries=3
            ),
            ServiceConfig(
                name="nlp",
                url="http://127.0.0.1:8002",
                health_check_path="/health",
                timeout=30.0,
                max_retries=3
            )
        ]
        
        for service_config in default_services:
            self.service_registry.register_service(service_config)
    
    def _create_fastapi_app(self) -> FastAPI:
        """
        创建FastAPI应用
        
        Returns:
            配置好的FastAPI应用实例
        """
        # 创建应用实例
        app = FastAPI(
            title="API网关",
            description="统一的微服务API网关",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 配置CORS
        if self.config.get('enable_cors', True):
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
        
        # 注册中间件
        self._register_middleware(app)
        
        # 注册路由
        self._register_routes(app)
        
        return app
    
    def _register_middleware(self, app: FastAPI):
        """
        注册中间件
        
        Args:
            app: FastAPI应用实例
        """
        
        @app.middleware("http")
        async def request_middleware(request: Request, call_next):
            """请求中间件：统计和限流"""
            start_time = time.time()
            
            # 简单的限流检查
            client_ip = request.client.host
            if not self._check_rate_limit(client_ip):
                return JSONResponse(
                    status_code=429,
                    content={"error": "请求频率过高，请稍后再试"}
                )
            
            # 处理请求
            try:
                response = await call_next(request)
                
                # 记录成功请求
                processing_time = time.time() - start_time
                self._record_gateway_request(True, processing_time)
                
                return response
                
            except Exception as e:
                # 记录失败请求
                processing_time = time.time() - start_time
                self._record_gateway_request(False, processing_time)
                
                self.logger.error(f"请求处理失败: {e}")
                return JSONResponse(
                    status_code=500,
                    content={"error": "内部服务器错误"}
                )
    
    def _register_routes(self, app: FastAPI):
        """
        注册API路由
        
        Args:
            app: FastAPI应用实例
        """
        
        @app.get("/health")
        async def gateway_health():
            """网关健康检查"""
            uptime = time.time() - self.start_time
            
            # 检查所有服务的健康状态
            service_health = {}
            for service_name, service_config in self.service_registry.get_all_services().items():
                is_available = self.service_registry.is_service_available(service_name)
                service_health[service_name] = {
                    'available': is_available,
                    'url': service_config.url
                }
            
            return {
                'status': 'healthy',
                'service': 'api-gateway',
                'version': '1.0.0',
                'uptime_seconds': uptime,
                'total_requests': self.total_requests,
                'services': service_health
            }
        
        @app.get("/api/v1/services")
        async def list_services():
            """列出所有注册的服务"""
            services = {}
            for service_name, service_config in self.service_registry.get_all_services().items():
                services[service_name] = {
                    'name': service_config.name,
                    'url': service_config.url,
                    'available': self.service_registry.is_service_available(service_name),
                    'stats': self.service_registry.get_service_stats().get(service_name, {})
                }
            
            return {
                'status': 'success',
                'data': {
                    'services': services,
                    'total_count': len(services)
                }
            }
        
        @app.get("/api/v1/stats")
        async def get_gateway_stats():
            """获取网关统计信息"""
            uptime = time.time() - self.start_time
            
            gateway_stats = {
                'gateway_info': {
                    'uptime_seconds': uptime,
                    'uptime_formatted': self._format_uptime(uptime)
                },
                'request_stats': {
                    'total_requests': self.total_requests,
                    'successful_requests': self.successful_requests,
                    'failed_requests': self.failed_requests,
                    'success_rate': (self.successful_requests / max(self.total_requests, 1)) * 100,
                    'avg_response_time': (self.total_response_time / max(self.total_requests, 1)) * 1000,  # ms
                    'requests_per_second': self.total_requests / max(uptime, 1)
                },
                'service_stats': self.service_registry.get_service_stats()
            }
            
            return {'status': 'success', 'data': gateway_stats}
        
        # 归属地服务路由
        @app.api_route("/api/v1/location/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
        async def location_proxy(path: str, request: Request):
            """归属地服务代理"""
            return await self._proxy_request("location", f"/api/v1/location/{path}", request)
        
        # NLP分析服务路由
        @app.api_route("/api/v1/nlp/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
        async def nlp_proxy(path: str, request: Request):
            """NLP分析服务代理"""
            return await self._proxy_request("nlp", f"/api/v1/{path}", request)
        
        # 兼容性路由：直接访问分析接口
        @app.api_route("/api/v1/analyze", methods=["POST"])
        async def analyze_proxy(request: Request):
            """文本分析代理（兼容性接口）"""
            return await self._proxy_request("nlp", "/api/v1/analyze", request)
        
        # 批量处理接口
        @app.post("/api/v1/process-phone")
        async def process_phone(request: Request):
            """
            完整的电话处理工作流
            整合归属地查询和文本分析
            """
            try:
                body = await request.json()
                phone_number = body.get('phone_number')
                mark_text = body.get('mark_text', '')
                
                if not phone_number:
                    raise HTTPException(status_code=400, detail="电话号码不能为空")
                
                start_time = time.time()
                
                # 并行调用归属地和NLP服务
                location_task = self._call_service("location", f"/api/v1/location/{phone_number}", {})
                nlp_task = self._call_service("nlp", "/api/v1/analyze", {
                    "text": mark_text,
                    "phone_number": phone_number
                })
                
                # 等待两个服务的响应
                location_result, nlp_result = await asyncio.gather(
                    location_task, nlp_task, return_exceptions=True
                )
                
                processing_time = (time.time() - start_time) * 1000
                
                # 构建响应
                response_data = {
                    'phone_number': phone_number,
                    'mark_text': mark_text,
                    'location_data': location_result if not isinstance(location_result, Exception) else None,
                    'nlp_analysis': nlp_result if not isinstance(nlp_result, Exception) else None,
                    'processing_time_ms': processing_time,
                    'timestamp': datetime.now().isoformat()
                }
                
                # 检查是否有错误
                errors = []
                if isinstance(location_result, Exception):
                    errors.append(f"归属地查询失败: {location_result}")
                if isinstance(nlp_result, Exception):
                    errors.append(f"文本分析失败: {nlp_result}")
                
                if errors:
                    response_data['errors'] = errors
                    response_data['status'] = 'partial_success' if len(errors) < 2 else 'failed'
                else:
                    response_data['status'] = 'success'
                
                return JSONResponse(content=response_data)
                
            except Exception as e:
                self.logger.error(f"电话处理工作流失败: {e}")
                return JSONResponse(
                    status_code=500,
                    content={'error': str(e), 'status': 'failed'}
                )
    
    async def _proxy_request(self, service_name: str, path: str, request: Request) -> Response:
        """
        代理请求到指定服务
        
        Args:
            service_name: 服务名称
            path: 请求路径
            request: 原始请求
            
        Returns:
            代理响应
        """
        try:
            # 检查服务是否可用
            if not self.service_registry.is_service_available(service_name):
                return JSONResponse(
                    status_code=503,
                    content={'error': f'服务 {service_name} 不可用'}
                )
            
            # 获取服务配置
            service_config = self.service_registry.get_service(service_name)
            if not service_config:
                return JSONResponse(
                    status_code=404,
                    content={'error': f'服务 {service_name} 未找到'}
                )
            
            # 构建目标URL
            target_url = f"{service_config.url}{path}"
            
            # 获取请求体
            body = None
            if request.method in ['POST', 'PUT', 'PATCH']:
                body = await request.body()
            
            # 转发请求
            start_time = time.time()
            
            if self.http_client:
                response = await self.http_client.request(
                    method=request.method,
                    url=target_url,
                    params=dict(request.query_params),
                    headers=dict(request.headers),
                    content=body,
                    timeout=service_config.timeout
                )
                
                response_time = time.time() - start_time
                
                # 记录请求结果
                success = 200 <= response.status_code < 400
                self.service_registry.record_request_result(service_name, success, response_time)
                
                # 返回响应
                return Response(
                    content=response.content,
                    status_code=response.status_code,
                    headers=dict(response.headers)
                )
            else:
                return JSONResponse(
                    status_code=503,
                    content={'error': 'HTTP客户端不可用'}
                )
                
        except Exception as e:
            self.logger.error(f"代理请求失败: {e}")
            
            # 记录失败
            response_time = time.time() - start_time if 'start_time' in locals() else 0
            self.service_registry.record_request_result(service_name, False, response_time)
            
            return JSONResponse(
                status_code=500,
                content={'error': f'代理请求失败: {str(e)}'}
            )
    
    async def _call_service(self, service_name: str, path: str, data: Dict[str, Any]) -> Any:
        """
        调用指定服务
        
        Args:
            service_name: 服务名称
            path: 请求路径
            data: 请求数据
            
        Returns:
            服务响应
        """
        if not self.service_registry.is_service_available(service_name):
            raise Exception(f"服务 {service_name} 不可用")
        
        service_config = self.service_registry.get_service(service_name)
        if not service_config:
            raise Exception(f"服务 {service_name} 未找到")
        
        target_url = f"{service_config.url}{path}"
        
        start_time = time.time()
        
        try:
            if self.http_client:
                if data:
                    response = await self.http_client.post(target_url, json=data, timeout=service_config.timeout)
                else:
                    response = await self.http_client.get(target_url, timeout=service_config.timeout)
                
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    self.service_registry.record_request_result(service_name, True, response_time)
                    return response.json()
                else:
                    self.service_registry.record_request_result(service_name, False, response_time)
                    raise Exception(f"服务返回错误: {response.status_code}")
            else:
                raise Exception("HTTP客户端不可用")
                
        except Exception as e:
            response_time = time.time() - start_time
            self.service_registry.record_request_result(service_name, False, response_time)
            raise e
    
    def _check_rate_limit(self, client_ip: str) -> bool:
        """
        检查请求限流
        
        Args:
            client_ip: 客户端IP
            
        Returns:
            是否允许请求
        """
        # 简单的滑动窗口限流
        current_time = time.time()
        window_size = 60  # 1分钟窗口
        max_requests = self.config.get('rate_limit_requests_per_minute', 1000)
        
        if client_ip not in self.rate_limiter:
            self.rate_limiter[client_ip] = []
        
        # 清理过期的请求记录
        self.rate_limiter[client_ip] = [
            req_time for req_time in self.rate_limiter[client_ip]
            if current_time - req_time < window_size
        ]
        
        # 检查是否超过限制
        if len(self.rate_limiter[client_ip]) >= max_requests:
            return False
        
        # 记录当前请求
        self.rate_limiter[client_ip].append(current_time)
        return True
    
    def _record_gateway_request(self, success: bool, response_time: float):
        """
        记录网关请求统计
        
        Args:
            success: 是否成功
            response_time: 响应时间
        """
        self.total_requests += 1
        self.total_response_time += response_time
        
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
    
    def _format_uptime(self, uptime_seconds: float) -> str:
        """
        格式化运行时间
        
        Args:
            uptime_seconds: 运行时间（秒）
            
        Returns:
            格式化的时间字符串
        """
        hours = int(uptime_seconds // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        seconds = int(uptime_seconds % 60)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def run(self, host: Optional[str] = None, port: Optional[int] = None):
        """
        运行API网关
        
        Args:
            host: 监听地址
            port: 监听端口
        """
        if not FASTAPI_AVAILABLE:
            self.logger.error("FastAPI不可用，无法启动API网关")
            return
        
        host = host or self.config.get('host', '127.0.0.1')
        port = port or self.config.get('port', 8000)
        
        self.logger.info(f"启动API网关: http://{host}:{port}")
        self.logger.info(f"API文档: http://{host}:{port}/docs")
        
        try:
            uvicorn.run(
                self.app,
                host=host,
                port=port,
                log_level=self.config.get('log_level', 'info').lower()
            )
        except Exception as e:
            self.logger.error(f"API网关启动失败: {e}")
    
    def close(self):
        """关闭网关和HTTP客户端"""
        if self.http_client:
            asyncio.create_task(self.http_client.aclose())


def create_api_gateway(config: Optional[Dict[str, Any]] = None) -> APIGateway:
    """
    创建API网关实例
    
    Args:
        config: 网关配置
        
    Returns:
        APIGateway实例
    """
    return APIGateway(config)


def main():
    """主函数：启动API网关"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建并启动API网关
    gateway = create_api_gateway()
    gateway.run()


if __name__ == '__main__':
    main()
