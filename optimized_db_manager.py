"""
优化的数据库管理器
采用保守的方式优化SQLite性能，同时保持向后兼容性
"""

import sqlite3
import threading
import logging
import time
from typing import Dict, List, Any, Optional
from contextlib import contextmanager
import json


class OptimizedDatabaseManager:
    """优化的数据库管理器"""
    
    def __init__(self, db_path: str = "phone_marks.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._local = threading.local()
        self._lock = threading.Lock()
        self._init_database()
        self._optimize_sqlite()
        
        # 性能统计
        self.stats = {
            'total_queries': 0,
            'total_time': 0.0,
            'slow_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # 简单的查询缓存
        self._query_cache = {}
        self._cache_max_size = 1000
        
    def _get_connection(self):
        """获取线程本地的数据库连接"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path, 
                check_same_thread=False,
                timeout=30.0  # 30秒超时
            )
            self._local.connection.row_factory = sqlite3.Row
            # 为每个连接应用优化设置
            self._optimize_connection(self._local.connection)
        
        return self._local.connection
    
    def _optimize_sqlite(self):
        """优化SQLite设置"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # SQLite性能优化设置
                optimizations = [
                    'PRAGMA journal_mode=WAL',        # 写前日志模式，提高并发性能
                    'PRAGMA synchronous=NORMAL',      # 平衡安全性和性能
                    'PRAGMA cache_size=10000',        # 增加缓存大小
                    'PRAGMA temp_store=MEMORY',       # 临时表存储在内存中
                    'PRAGMA mmap_size=268435456',     # 256MB内存映射
                    'PRAGMA optimize'                 # 优化查询计划
                ]
                
                for pragma in optimizations:
                    try:
                        cursor.execute(pragma)
                        self.logger.debug(f"应用优化设置: {pragma}")
                    except Exception as e:
                        self.logger.warning(f"优化设置失败 {pragma}: {e}")
                
                conn.commit()
                self.logger.info("SQLite优化设置已应用")
                
        except Exception as e:
            self.logger.error(f"SQLite优化失败: {e}")
    
    def _optimize_connection(self, conn):
        """为单个连接应用优化设置"""
        try:
            cursor = conn.cursor()
            cursor.execute('PRAGMA cache_size=5000')
            cursor.execute('PRAGMA temp_store=MEMORY')
        except Exception as e:
            self.logger.warning(f"连接优化失败: {e}")
    
    def _init_database(self):
        """初始化数据库结构"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查并创建必要的索引
                self._create_indexes(cursor)
                
                # 检查表结构并添加缺失字段
                self._ensure_table_structure(cursor)
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_indexes(self, cursor):
        """创建性能优化索引"""
        indexes = [
            # dial_records表索引
            'CREATE INDEX IF NOT EXISTS idx_dial_phone_number ON dial_records(phone_number)',
            'CREATE INDEX IF NOT EXISTS idx_dial_time ON dial_records(dial_time)',
            'CREATE INDEX IF NOT EXISTS idx_dial_status ON dial_records(status)',
            'CREATE INDEX IF NOT EXISTS idx_dial_method ON dial_records(method)',
            'CREATE INDEX IF NOT EXISTS idx_dial_province_city ON dial_records(province, city)',
            
            # 复合索引用于常见查询
            'CREATE INDEX IF NOT EXISTS idx_dial_phone_time ON dial_records(phone_number, dial_time)',
            'CREATE INDEX IF NOT EXISTS idx_dial_status_time ON dial_records(status, dial_time)',
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                self.logger.debug(f"创建索引: {index_sql}")
            except Exception as e:
                self.logger.warning(f"索引创建失败: {e}")
    
    def _ensure_table_structure(self, cursor):
        """确保表结构包含所有必要字段"""
        try:
            # 检查dial_records表结构
            cursor.execute("PRAGMA table_info(dial_records)")
            columns = [column[1] for column in cursor.fetchall()]
            
            # 需要的字段
            required_columns = {
                'isp': 'TEXT',
                'location_source': 'TEXT',
                'processing_time': 'REAL',
                'retry_count': 'INTEGER DEFAULT 0'
            }
            
            # 添加缺失的字段
            for column_name, column_type in required_columns.items():
                if column_name not in columns:
                    try:
                        cursor.execute(f'ALTER TABLE dial_records ADD COLUMN {column_name} {column_type}')
                        self.logger.info(f"添加字段: {column_name}")
                    except Exception as e:
                        self.logger.warning(f"添加字段失败 {column_name}: {e}")
                        
        except Exception as e:
            self.logger.error(f"表结构检查失败: {e}")
    
    @contextmanager
    def _timed_query(self, query_type: str = "unknown"):
        """查询性能计时上下文管理器"""
        start_time = time.time()
        try:
            yield
        finally:
            elapsed = time.time() - start_time
            self.stats['total_queries'] += 1
            self.stats['total_time'] += elapsed
            
            # 记录慢查询
            if elapsed > 0.1:  # 100ms以上认为是慢查询
                self.stats['slow_queries'] += 1
                self.logger.warning(f"慢查询检测: {query_type} 耗时 {elapsed:.3f}秒")
    
    def save_dial_record(self, record: Dict) -> bool:
        """保存拨号记录 - 优化版本"""
        try:
            with self._timed_query("save_dial_record"):
                with self._get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # 检查表结构是否包含新字段
                    cursor.execute("PRAGMA table_info(dial_records)")
                    columns = [column[1] for column in cursor.fetchall()]
                    
                    # 根据表结构决定插入语句
                    if 'isp' in columns and 'location_source' in columns:
                        cursor.execute('''
                            INSERT INTO dial_records 
                            (phone_number, mark_info, province, city, location, isp, location_source, 
                             phone_model, android_version, status, screenshot_path, processing_time, method, retry_count)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            record['phone_number'],
                            record['mark_info'],
                            record.get('province', ''),
                            record.get('city', ''),
                            record.get('location', ''),
                            record.get('isp', ''),
                            record.get('location_source', ''),
                            record['phone_model'],
                            record['android_version'],
                            record['status'],
                            record.get('screenshot_path', ''),
                            record.get('processing_time', 0),
                            record.get('method', 'ML Kit'),
                            record.get('retry_count', 0)
                        ))
                    else:
                        # 兼容旧表结构
                        cursor.execute('''
                            INSERT INTO dial_records 
                            (phone_number, mark_info, province, city, location, phone_model, android_version, status, 
                             screenshot_path, processing_time, method)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            record['phone_number'],
                            record['mark_info'],
                            record.get('province', ''),
                            record.get('city', ''),
                            record.get('location', ''),
                            record['phone_model'],
                            record['android_version'],
                            record['status'],
                            record.get('screenshot_path', ''),
                            record.get('processing_time', 0),
                            record.get('method', 'ML Kit')
                        ))
                    
                    conn.commit()
                    return True
                    
        except Exception as e:
            self.logger.error(f"保存记录失败: {e}")
            return False
    
    def get_recent_records(self, limit: int = 100, use_cache: bool = True) -> List[Dict]:
        """获取最近记录 - 带缓存优化"""
        cache_key = f"recent_records_{limit}"
        
        # 检查缓存
        if use_cache and cache_key in self._query_cache:
            self.stats['cache_hits'] += 1
            return self._query_cache[cache_key]
        
        try:
            with self._timed_query("get_recent_records"):
                with self._get_connection() as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        SELECT * FROM dial_records 
                        ORDER BY dial_time DESC 
                        LIMIT ?
                    ''', (limit,))
                    
                    records = [dict(row) for row in cursor.fetchall()]
                    
                    # 更新缓存
                    if use_cache:
                        self._update_cache(cache_key, records)
                        self.stats['cache_misses'] += 1
                    
                    return records
                    
        except Exception as e:
            self.logger.error(f"获取记录失败: {e}")
            return []
    
    def _update_cache(self, key: str, value: Any):
        """更新查询缓存"""
        if len(self._query_cache) >= self._cache_max_size:
            # 简单的LRU：删除一半缓存
            keys_to_remove = list(self._query_cache.keys())[:self._cache_max_size // 2]
            for k in keys_to_remove:
                del self._query_cache[k]
        
        self._query_cache[key] = value
    
    def clear_cache(self):
        """清空查询缓存"""
        self._query_cache.clear()
        self.logger.info("查询缓存已清空")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_query_time = self.stats['total_time'] / max(self.stats['total_queries'], 1)
        cache_hit_rate = self.stats['cache_hits'] / max(
            self.stats['cache_hits'] + self.stats['cache_misses'], 1
        )
        
        return {
            'total_queries': self.stats['total_queries'],
            'avg_query_time': round(avg_query_time * 1000, 2),  # 毫秒
            'slow_queries': self.stats['slow_queries'],
            'cache_hit_rate': round(cache_hit_rate * 100, 1),  # 百分比
            'cache_size': len(self._query_cache)
        }
    
    def optimize_database(self):
        """手动触发数据库优化"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 分析表统计信息
                cursor.execute('ANALYZE')
                
                # 重建索引
                cursor.execute('REINDEX')
                
                # 清理碎片
                cursor.execute('VACUUM')
                
                conn.commit()
                self.logger.info("数据库优化完成")
                
        except Exception as e:
            self.logger.error(f"数据库优化失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')


def test_optimized_db():
    """测试优化的数据库管理器"""
    db = OptimizedDatabaseManager()
    
    # 测试保存记录
    test_record = {
        'phone_number': '13800138000',
        'mark_info': '测试标记',
        'province': '北京',
        'city': '北京',
        'isp': '中国移动',
        'location_source': 'database',
        'phone_model': 'Test Phone',
        'android_version': '10',
        'status': 'success',
        'processing_time': 2.5,
        'method': 'ML Kit',
        'retry_count': 0
    }
    
    print("测试保存记录...")
    success = db.save_dial_record(test_record)
    print(f"保存结果: {success}")
    
    # 测试查询记录
    print("测试查询记录...")
    records = db.get_recent_records(5)
    print(f"查询到 {len(records)} 条记录")
    
    # 测试缓存
    print("测试缓存...")
    records2 = db.get_recent_records(5)  # 应该命中缓存
    
    # 获取性能统计
    stats = db.get_performance_stats()
    print("性能统计:", stats)
    
    db.close()


if __name__ == '__main__':
    test_optimized_db()
